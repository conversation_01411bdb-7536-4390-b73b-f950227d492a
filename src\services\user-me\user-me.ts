import { authenticate } from '@feathersjs/authentication'

import type { Application } from '../../declarations'
import { UserMeService } from './user-me.class'
// import { userPatchValidator } from '../users/users.schema' // Assuming you have a patch validator
// import { schemaHooks } from '@feathersjs/schema'

export const userMe = (app: Application) => {
  // Register the /users/me service BEFORE the main users service
  app.use('users/me', new UserMeService(app), {
    methods: ['find', 'patch'], // Changed 'get' to 'find'
    events: [] // No custom events needed
  })

  // Add authentication and validation hooks
  app.service('users/me').hooks({
    around: {
      all: [
        // schemaHooks.resolveExternal(userExternalResolver),
        // schemaHooks.resolveResult(userResolver)
      ]
    },
    before: {
      all: [authenticate('jwt')], // Require authentication for all methods
      get: [],
      patch: [
        // Add validation hooks for profile updates
        // schemaHooks.validateData(userPatchValidator) // Example, if you have a patch validator
      ]
    },
    after: {
      all: []
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
/* eslint-disable @typescript-eslint/no-empty-interface */
declare module '../../declarations' {
  interface ServiceTypes {
    'users/me': UserMeService
  }
}
