import { disallow } from 'feathers-hooks-common';

const sendWelcomeEmail = async (context: any) => {
  const { app, result } = context;
  const emailService = app.service('/_mailer');

  if (result && result.email) {
    try {
      await emailService.create({
        to: result.email,
        subject: 'Welcome to Our App!',
        html: `
          <h1>Welcome ${result.name || result.email}!</h1>
          <p>Thank you for registering with our application.</p>
          <p>Your account has been successfully created.</p>
        `,
        text: `Welcome ${result.name || result.email}! Thank you for registering with our application.`
      });
    } catch (error) {
      console.error('Failed to send welcome email:', error);
      // Don't fail the registration if email fails
    }
  }

  return context;
};

export default {
  before: {
    all: [],
    find: [disallow('external')],
    get: [disallow('external')],
    create: [],
    update: [disallow('external')],
    patch: [disallow('external')],
    remove: [disallow('external')]
  },
  after: {
    all: [],
    find: [],
    get: [],
    create: [sendWelcomeEmail],
    update: [],
    patch: [],
    remove: []
  },
  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
};
