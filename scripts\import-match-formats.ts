import fs from 'fs';
import path from 'path';

import { parse } from 'csv-parse';

import { app } from '../src/app';
import type { MatchFormatData } from '../src/services/match-formats/match-formats.schema';

// Define an interface for Feathers error type
interface FeathersError extends Error {
  code: number;
  className: string;
  data?: any[];
}

/**
 * Convert string to boolean
 */
const parseBool = (value: string | undefined): boolean | undefined => {
  if (value === undefined || value === '' || value === '\\N') return undefined;
  if (value === '0') return false;
  if (value === '1') return true;
  return undefined;
};

/**
 * Parse string value, handling null values
 */
const parseString = (value: string | undefined): string | undefined => {
  if (value === undefined || value === '' || value === '\\N') return undefined;
  return value;
};

/**
 * Parse number value, handling null values
 */
const parseNumber = (value: string | undefined): number | undefined => {
  if (value === undefined || value === '' || value === '\\N') return undefined;
  const num = Number(value);
  return isNaN(num) ? undefined : num;
};

/**
 * Parse JSON field, handling null values and stringified JSON
 */
const parseJsonField = (value: string | undefined): any => {
  if (value === undefined || value === '' || value === '\\N') return undefined;
  try {
    return JSON.parse(value);
  } catch (err) {
    console.warn(`Failed to parse JSON field: ${value}`);
    return undefined;
  }
};

/**
 * Transform typesOfTargets content: replace "Tarcza" with "PAPER"
 */
const transformTypesOfTargets = (value: string | undefined): string | undefined => {
  if (!value) return value;
  return value.replace(/Tarcza/g, 'PAPER');
};

/**
 * Auto-detect CSV delimiter
 */
function detectDelimiter(csvContent: string): string {
  const firstLine = csvContent.split('\n')[0];
  const delimiters = [';', '\t', ','];

  for (const delimiter of delimiters) {
    if (firstLine.includes(delimiter)) {
      return delimiter;
    }
  }

  return ','; // Default fallback
}

/**
 * Main import function for match formats from CSV
 */
async function importMatchFormats(csvFilePath: string) {
  const matchFormats = app.service('match-formats');
  const federations = app.service('federations');

  // Build federation lookup map: legacyId -> id
  const federationList = await federations.find({ paginate: false });
  const federationLookup = new Map<number, number>();

  if (Array.isArray(federationList)) {
    for (const federation of federationList) {
      if (federation.legacyId) {
        federationLookup.set(federation.legacyId, federation.id);
      }
    }
  }

  console.log(`Loaded ${federationLookup.size} federations for lookup`);

  // Ensure the path is absolute
  const filePath = path.isAbsolute(csvFilePath)
    ? csvFilePath
    : path.join(process.cwd(), csvFilePath);

  console.log(`Reading CSV file: ${filePath}`);

  // Read file content to detect delimiter
  let csvContent = fs.readFileSync(filePath, 'utf-8');
  // Remove UTF-8 BOM if present
  if (csvContent.charCodeAt(0) === 0xFEFF) {
    csvContent = csvContent.slice(1);
  }
  const delimiter = detectDelimiter(csvContent);
  console.log(`Detected delimiter: "${delimiter}"`);

  // Parse CSV with detected delimiter
  const records = await new Promise<any[]>((resolve, reject) => {
    const results: any[] = [];

    // Use a stream from a buffer to avoid BOM issues
    const { Readable } = require('stream');
    const stream = Readable.from([csvContent]);
    stream
      .pipe(parse({
        delimiter,
        columns: true,
        skip_empty_lines: true,
        trim: true
      }))
      .on('data', (data: Record<string, unknown>) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', reject);
  });

  console.log(`Found ${records.length} records to import`);

  let importedCount = 0;
  let skippedCount = 0;

  for (const row of records) {
    try {
      // Skip inactive records if needed
      if (parseBool(row['active']) === false) {
        console.log(`Skipping inactive match format: ${row['name']} (id: ${row['id']})`);
        skippedCount++;
        continue;
      }

      // Resolve federation ID from legacyId
      const federationLegacyId = parseNumber(row['federation']);
      const federationId = federationLegacyId ? federationLookup.get(federationLegacyId) : undefined;

      if (federationLegacyId && !federationId) {
        console.warn(`Federation with legacyId ${federationLegacyId} not found for match format: ${row['name']}`);
      }

      // Parse and transform the data according to field mappings
      const scoringData = parseJsonField(row['punctation']);
      const data: MatchFormatData = {
        legacyId: parseNumber(row['id']),
        active: parseBool(row['active']) ?? true,
        name: parseString(row['name']) || 'Unnamed Match Format',
        numberOfShots: parseNumber(row['number_of_arrows']), // goal > target mapping
        typesOfTargets: transformTypesOfTargets(parseString(row['type_of_goals'])), // Replace "Tarcza" with "PAPER"
        targets: parseNumber(row['goals']), // goal > target mapping
        scoring: scoringData ? JSON.stringify(scoringData) : undefined, // punctation > scoring mapping, stringify for JSONB
        matchType: parseString(row['competition_branch']), // competition_branch > matchType mapping
        federationId
      };

      // Remove undefined fields to avoid DB DEFAULT issues
      const cleanData = Object.fromEntries(
        Object.entries(data).filter(([_, v]) => v !== undefined)
      ) as MatchFormatData;

      // Debug: log prepared data
      console.log('Prepared match format data:', {
        legacyId: row['id'],
        name: cleanData.name,
        numberOfShots: cleanData.numberOfShots,
        typesOfTargets: cleanData.typesOfTargets,
        targets: cleanData.targets,
        matchType: cleanData.matchType,
        federationId: cleanData.federationId,
        scoringType: typeof cleanData.scoring
      });

      const createdFormat = await matchFormats.create(cleanData);

      console.log(`Imported match format: ${cleanData.name} (legacyId: ${row['id']}, federationId: ${cleanData.federationId})`);
      importedCount++;
    } catch (err) {
      console.error(
        `Failed to import match format with id=${row['id']}, name=${row['name']}:`,
        (err as any).message
      );
    }
  }

  console.log(`\nImport completed:`);
  console.log(`- Imported: ${importedCount} match formats`);
  console.log(`- Skipped: ${skippedCount} match formats`);
}

if (process.argv.length < 3) {
  console.error('Usage: pnpm tsx scripts/import-match-formats.ts <path-to-csv>')
  process.exit(1)
}

importMatchFormats(process.argv[2])
  .then(() => {
    console.log('Match formats import complete.')
    process.exit(0)
  })
  .catch((err) => {
    console.error('Import failed:', err)
    process.exit(1)
  })
