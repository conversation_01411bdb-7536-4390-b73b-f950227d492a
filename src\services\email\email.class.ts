// src/services/email/email.class.ts
import { Application } from '@feathersjs/feathers';

import { EmailConfig, validateEmailConfig } from './email.config';
import { EmailOptions, EmailProvider } from './providers/email-provider.interface';
import { MockEmailProvider } from './providers/mock-provider';
import { SMTPEmailProvider } from './providers/smtp-provider';
import { TestEmailProvider } from './providers/test-provider';

export class EmailService {
  private provider: EmailProvider; 
  private app: Application;

  constructor(app: Application) {
    this.app = app;
    this.provider = this.createProvider();
  }

  private createProvider(): EmailProvider {
    const env = process.env.NODE_ENV || 'development';
    const emailConfig: EmailConfig = validateEmailConfig(this.app.get('email'));
    
    // Test environment - always use TestEmailProvider
    if (env === 'test') {
      return new TestEmailProvider();
    }
    
    // Check explicit provider configuration first
    if (emailConfig?.provider) {
      switch (emailConfig.provider) {
        case 'smtp':
          return new SMTPEmailProvider(emailConfig.smtp || emailConfig);
        case 'mock':
          return new MockEmailProvider();
        case 'test':
          return new TestEmailProvider();
        default:
          console.warn(`Unknown email provider: ${emailConfig.provider}, falling back to mock`);
          return new MockEmailProvider();
      }
    }
    
    // Environment-based defaults when no explicit provider is set
    switch (env) {
      case 'development':
      case 'staging':
        return new MockEmailProvider();
      
      case 'production':
        // In production, require explicit SMTP configuration
        if (!emailConfig?.host && !emailConfig?.smtp?.host) {
          console.warn('No email configuration found in production, using MockEmailProvider');
          return new MockEmailProvider();
        }
        return new SMTPEmailProvider(emailConfig.smtp || emailConfig);
      
      default:
        console.warn(`Unknown NODE_ENV: ${env}, using MockEmailProvider`);
        return new MockEmailProvider();
    }
  }

  async create(data: EmailOptions): Promise<any> {
    try {
      // Add default from address if not provided
      if (!data.from) {
        const emailConfig = this.app.get('email');
        data.from = emailConfig?.defaultFrom || '<EMAIL>';
      }

      return await this.provider.send(data);
    } catch (error) {
      console.error('Email sending failed:', error);
      throw error;
    }
  }

  // Method to get provider for testing
  getProvider(): EmailProvider {
    return this.provider;
  }
}
