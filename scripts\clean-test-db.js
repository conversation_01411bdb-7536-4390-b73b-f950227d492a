/**
 * <PERSON><PERSON><PERSON> to clean the test database by dropping and recreating it
 */
const { Client } = require('pg');
const { spawn } = require('child_process');

async function runCommand(command, args) {
  return new Promise((resolve, reject) => {
    const proc = spawn(command, args, { stdio: 'inherit' });

    proc.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command ${command} ${args.join(' ')} failed with code ${code}`));
      }
    });
  });
}

async function cleanTestDatabase() {
  // Connect to the default postgres database
  const client = new Client({
    user: 'postgres',
    host: 'localhost',
    password: 'cruARCHERY7*',
    port: 5432,
    database: 'postgres'
  });

  try {
    await client.connect();

    // Check if the test database exists
    const res = await client.query(
      "SELECT 1 FROM pg_database WHERE datname = 'archery_test'"
    );

    // If the database exists, drop it and recreate it
    if (res.rowCount > 0) {
      console.log('Dropping archery_test database...');

      // Terminate all connections to the database first
      await client.query(`
        SELECT pg_terminate_backend(pg_stat_activity.pid)
        FROM pg_stat_activity
        WHERE pg_stat_activity.datname = 'archery_test'
        AND pid <> pg_backend_pid()
      `);

      await client.query('DROP DATABASE archery_test');
      console.log('Test database dropped');
    }

    // Create the database
    console.log('Creating fresh archery_test database...');
    await client.query('CREATE DATABASE archery_test');
    console.log('Fresh test database created successfully');

  } catch (err) {
    console.error('Error cleaning test database:', err);
    process.exit(1);
  } finally {
    await client.end();
  }
}

cleanTestDatabase();
