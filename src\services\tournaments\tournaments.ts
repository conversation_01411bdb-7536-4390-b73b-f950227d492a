import { createSwaggerServiceOptions } from 'feathers-swagger'

// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import { authenticate } from '@feathersjs/authentication'
import { hooks as schemaHooks } from '@feathersjs/schema'

import type { Application } from '../../declarations'
import { populateUserTracking, skipIfDeletedByHook } from '../../hooks/user-tracking'
import { validateOrganizer } from './hooks/validate-organizer'
import { TournamentsService, getOptions } from './tournaments.class'
import {
  tournamentDataResolver,
  tournamentDataSchema,
  tournamentDataValidator,
  tournamentExternalResolver,
  tournamentPatchResolver,
  tournamentPatchSchema,
  tournamentPatchValidator,
  tournamentQueryResolver,
  tournamentQuerySchema,
  tournamentQueryValidator,
  tournamentResolver,
  tournamentSchema
} from './tournaments.schema'
import { tournamentsMethods, tournamentsPath } from './tournaments.shared'

export * from './tournaments.class'
export * from './tournaments.schema'

// A configure function that registers the service and its hooks via `app.configure`
export const tournaments = (app: Application) => {
  // Register our service on the Feathers application
  app.use(tournamentsPath, new TournamentsService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: tournamentsMethods,
    // You can add additional custom events to be sent to clients here
    events: [],
    docs: createSwaggerServiceOptions({
      schemas: { tournamentDataSchema, tournamentQuerySchema, tournamentSchema, tournamentPatchSchema }, // Updated schema names
      docs: {
        description: 'Tournaments service',
        securities: ['all']
      }
    })
  })
  // Initialize hooks
  app.service(tournamentsPath).hooks({
    around: {
      all: [
        authenticate('jwt'),
        schemaHooks.resolveExternal(tournamentExternalResolver), // Updated resolver name
        schemaHooks.resolveResult(tournamentResolver) // Updated resolver name
      ]
    },
    before: {
      all: [
        skipIfDeletedByHook(),
        schemaHooks.validateQuery(tournamentQueryValidator), // Updated validator name
        schemaHooks.resolveQuery(tournamentQueryResolver) // Updated resolver name
      ],
      find: [],
      get: [],
      create: [
        schemaHooks.validateData(tournamentDataValidator), // Updated validator name
        validateOrganizer(),
        schemaHooks.resolveData(tournamentDataResolver), // Updated resolver name
        populateUserTracking()
      ],
      patch: [
        schemaHooks.validateData(tournamentPatchValidator), // Updated validator name
        validateOrganizer(),
        schemaHooks.resolveData(tournamentPatchResolver), // Updated resolver name
        populateUserTracking()
      ],
      remove: [populateUserTracking()]
    },
    after: {
      all: []
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    [tournamentsPath]: TournamentsService
  }
}
