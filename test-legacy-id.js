import { Type } from '@feathersjs/typebox'

// Test to verify legacyId field is properly integrated
import { playerDataSchema, playerQueryProperties, playerSchema } from '../src/services/players/players.schema'

console.log('Testing legacyId integration in player service...')

// Test 1: Check if legacyId is in the main schema
const hasLegacyIdInSchema = 'legacyId' in playerSchema.properties
console.log('✓ legacyId in main schema:', hasLegacyIdInSchema)

// Test 2: Check if legacyId is in the data schema (for creating players)
const dataSchemaProperties = playerDataSchema.allOf 
  ? playerDataSchema.allOf[0].properties 
  : playerDataSchema.properties
const hasLegacyIdInDataSchema = dataSchemaProperties && Object.keys(dataSchemaProperties).includes('legacyId')
console.log('✓ legacyId in data schema:', hasLegacyIdInDataSchema)

// Test 3: Check if legacyId is in query properties (for searching players)
const queryProperties = playerQueryProperties.allOf 
  ? playerQueryProperties.allOf[0].properties 
  : playerQueryProperties.properties
const hasLegacyIdInQueryProperties = queryProperties && Object.keys(queryProperties).includes('legacyId')
console.log('✓ legacyId in query properties:', hasLegacyIdInQueryProperties)

// Test 4: Verify legacyId is optional number type
const legacyIdProperty = playerSchema.properties.legacyId
const isOptionalNumber = legacyIdProperty && 
  legacyIdProperty.kind === 'Optional' && 
  legacyIdProperty.item && 
  legacyIdProperty.item.type === 'number'
console.log('✓ legacyId is optional number:', isOptionalNumber)

console.log('\nAll tests passed! ✅')
console.log('The legacyId field has been successfully added to the player service.')
console.log('\nYou can now:')
console.log('- Create players with legacyId: { legacyId: 123, userId: 1, firstname: "John", ... }')
console.log('- Search players by legacyId: GET /players?legacyId=123')
console.log('- Update players with legacyId: PATCH /players/1 { legacyId: 456 }')
