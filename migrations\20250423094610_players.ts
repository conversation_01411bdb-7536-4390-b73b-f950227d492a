// For more information about this file see https://dove.feathersjs.com/guides/cli/knexfile.html
import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable('players', table => {
    table.increments('id')
    table.integer('userId').references('id').inTable('users').notNullable()
    table.boolean('isActive').defaultTo(true)
    table.string('address')
    table.string('zipcode')
    table.string('city')
    table.string('country')
    table.string('phone')
    table.date('birthdate')
    table.string('sex')
    table.string('firstname')
    table.string('lastname')
    table.decimal('latitude', 9, 6)
    table.decimal('longitude', 9, 6)
    table.jsonb('equipmentCategory')
    table.string('avatar')
    table.jsonb('licenses')
    table.timestamp('activatedAt').nullable()
    table.timestamp('createdAt').defaultTo(knex.fn.now())
    table.timestamp('updatedAt').defaultTo(knex.fn.now())
    table.timestamp('deletedAt').nullable()
  })
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('players')
}
