import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('users', (table) => {
    // Authentication management fields
    table.boolean('isVerified').defaultTo(false)
    table.string('verifyToken').nullable()
    table.string('verifyShortToken').nullable()
    table.timestamp('verifyExpires').nullable()
    table.jsonb('verifyChanges').nullable()
    table.string('resetToken').nullable()
    table.string('resetShortToken').nullable()
    table.timestamp('resetExpires').nullable()
    table.integer('resetAttempts').defaultTo(0)

    // Avatar field is already added in 20250418162729_chat.ts migration
  })
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('users', (table) => {
    table.dropColumn('isVerified')
    table.dropColumn('verifyToken')
    table.dropColumn('verifyShortToken')
    table.dropColumn('verifyExpires')
    table.dropColumn('verifyChanges')
    table.dropColumn('resetToken')
    table.dropColumn('resetShortToken')
    table.dropColumn('resetExpires')
    table.dropColumn('resetAttempts')
    // Avatar field is managed by 20250418162729_chat.ts migration
  })
}
