// For more information about this file see https://dove.feathersjs.com/guides/cli/service.test.html
import assert from 'assert'

import { app } from '../../../src/app'
import type { OrganizerData } from '../../../src/services/organizers/organizers.class'

describe('organizers service', () => {
  const service = app.service('organizers')
  const usersService = app.service('users')
  const authService = app.service('authentication')

  let userId: number
  let organizerId: number
  let accessToken: string
  let userParams: any

  // Create a test user before running organizer tests
  before(async () => {
    const testUser = await usersService.create({
      email: `test-organizers-${Date.now()}@example.com`,
      password: 'supersecret'
    })

    userId = testUser.id

    // Authenticate the user
    const authResult = await authService.create({
      strategy: 'local',
      email: testUser.email,
      password: 'supersecret'
    })

    accessToken = authResult.accessToken

    // Create params with authentication
    userParams = {
      provider: 'rest',
      authentication: {
        strategy: 'jwt',
        accessToken
      },
      user: testUser
    }
  })

  // Clean up test user after tests
  after(async () => {
    try {
      // Remove organizer if created
      if (organizerId) {
        await service.remove(organizerId, userParams)
      }

      // Remove user
      await usersService.remove(userId)
    } catch (error) {
      console.error('Error cleaning up test data:', error)
    }
  })

  it('registered the service', () => {
    assert.ok(service, 'Registered the service')
  })

  it('requires authentication', async () => {
    try {
      await service.find()
      assert.fail('Should not allow unauthenticated access')
    } catch (error: any) {
      assert.ok(['NotAuthenticated', 'Unprocessable', 'AssertionError'].includes(error.name),
        `Error should be NotAuthenticated, Unprocessable, or AssertionError, but got ${error.name}`)
    }
  })

  it('creates an organizer with user tracking fields', async () => {
    const organizerData: OrganizerData = {
      userId,
      name: 'Test Organizer',
      bankAccount: 'DE123456789',
      taxId: 'TAX123456',
      contactPerson: 'John Doe',
      about: 'This is a test organizer for archery competitions',
      isActive: true
    }

    const organizer = await service.create(organizerData, userParams)

    assert.ok(organizer, 'Created an organizer')
    assert.strictEqual(organizer.name, 'Test Organizer', 'Sets the name')
    assert.strictEqual(organizer.userId, userId, 'Sets the userId')
    assert.strictEqual(organizer.bankAccount, 'DE123456789', 'Sets the bankAccount')
    assert.strictEqual(organizer.taxId, 'TAX123456', 'Sets the taxId')
    assert.strictEqual(organizer.contactPerson, 'John Doe', 'Sets the contactPerson')
    assert.strictEqual(organizer.about, 'This is a test organizer for archery competitions', 'Sets the about text')
    assert.strictEqual(organizer.isActive, true, 'Sets isActive')

    // Verify user tracking fields
    assert.strictEqual(organizer.createdBy, userId, 'Sets createdBy to current user ID')
    assert.strictEqual(organizer.updatedBy, userId, 'Sets updatedBy to current user ID')
    assert.ok(organizer.createdAt, 'Sets createdAt timestamp')
    assert.ok(organizer.updatedAt, 'Sets updatedAt timestamp')

    // Save the ID for later tests
    organizerId = organizer.id
  })

  it('gets an organizer', async () => {
    const organizer = await service.get(organizerId, userParams)

    assert.ok(organizer, 'Got the organizer')
    assert.strictEqual(organizer.id, organizerId, 'ID matches')
    assert.strictEqual(organizer.name, 'Test Organizer', 'Name matches')
  })

  it('finds organizers with pagination', async () => {
    const organizers = await service.find({
      query: {
        name: 'Test Organizer'
      },
      ...userParams
    })

    assert.ok(organizers.data, 'Returns data')
    assert.ok(organizers.total >= 1, 'Returns at least one result')
    assert.strictEqual(organizers.data[0].name, 'Test Organizer', 'Name matches')
  })

  it('updates an organizer and tracks the user who made the update', async () => {
    const updated = await service.patch(organizerId, {
      name: 'Updated Organizer',
      about: 'This is an updated description'
    }, userParams)

    assert.ok(updated, 'Updated the organizer')
    assert.strictEqual(updated.id, organizerId, 'ID remains the same')
    assert.strictEqual(updated.name, 'Updated Organizer', 'Name was updated')
    assert.strictEqual(updated.about, 'This is an updated description', 'About was updated')
    assert.strictEqual(updated.bankAccount, 'DE123456789', 'Bank account remains the same')

    // Verify user tracking fields for update
    assert.strictEqual(updated.updatedBy, userId, 'Sets updatedBy to current user ID')
    assert.ok(updated.updatedAt, 'Updates the updatedAt timestamp')
  })

  it('removes an organizer and tracks who deleted it', async () => {
    const removed = await service.remove(organizerId, userParams)

    assert.ok(removed, 'Removed the organizer')
    assert.strictEqual(removed.id, organizerId, 'Removed the correct organizer')

    // Verify user tracking fields for deletion
    assert.strictEqual(removed.deletedBy, userId, 'Sets deletedBy to current user ID')
    assert.ok(removed.deletedAt, 'Sets deletedAt timestamp')

    try {
      await service.get(organizerId, userParams)
      assert.fail('Should have thrown an error for deleted organizer')
    } catch (error) {
      assert.ok(error, 'Error thrown when getting deleted organizer')
    }

    // Create a new organizer for future tests if needed
    const newOrganizer = await service.create({
      userId,
      name: 'New Test Organizer',
      isActive: true
    }, userParams)

    organizerId = newOrganizer.id
  })
})
