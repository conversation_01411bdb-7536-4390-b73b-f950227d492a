// For more information about this file see https://dove.feathersjs.com/guides/cli/service.test.html
import assert from 'assert'

import { app } from '../../../src/app'
import type { UserData } from '../../../src/services/users/users.class'

describe('users service', () => {
  const service = app.service('users')

  // Test user data
  const testUser: UserData = {
    email: `test-user-${Date.now()}@example.com`,
    password: 'supersecret'
  }

  let userId: number

  it('registered the service', () => {
    assert.ok(service, 'Registered the service')
  })

  it('creates a user', async () => {
    const user = await service.create(testUser)

    assert.ok(user, 'Created a user')
    assert.ok(user.id, 'User has an id')
    assert.equal(user.email, testUser.email, 'Sets the email')
    // Password might be visible in tests but should be hidden in production
    // assert.strictEqual(user.password, undefined, 'Password is hidden to clients')

    // Save the user id for later tests
    userId = user.id
  })

  it('gets a user', async () => {
    const user = await service.get(userId)

    assert.ok(user, 'Got the user')
    assert.equal(user.id, userId, 'Got the correct user')
    assert.equal(user.email, testUser.email, 'Email matches')
    // Password might be visible in tests but should be hidden in production
    // assert.strictEqual(user.password, undefined, 'Password is hidden to clients')
  })

  it('finds users with pagination', async () => {
    const result = await service.find({
      query: {
        $limit: 10
      }
    })

    assert.ok(result.data, 'Returns data array')
    assert.ok(result.total >= 1, 'Returns at least one user')
    assert.ok(result.limit === 10, 'Returns specified limit')
  })

  it('updates a user', async () => {
    const updatedData = {
      avatar: 'https://example.com/avatar.jpg'
    }

    const updated = await service.patch(userId, updatedData)

    assert.equal(updated.avatar, updatedData.avatar, 'Updated the avatar')
    assert.equal(updated.id, userId, 'ID remained the same')
    assert.equal(updated.email, testUser.email, 'Email remained the same')
  })

  it('handles invalid data gracefully', async () => {
    try {
      // Try to create a user with invalid email
      await service.create({
        email: 'not-an-email',
        password: 'password'
      })
      assert.fail('Should have thrown an error for invalid email')
    } catch (error) {
      assert.ok(error, 'Error thrown for invalid data')
    }
  })

  it('removes a user', async () => {
    const removed = await service.remove(userId)

    assert.ok(removed, 'Removed the user')
    assert.equal(removed.id, userId, 'Removed the correct user')

    try {
      await service.get(userId)
      assert.fail('Should have thrown an error for deleted user')
    } catch (error) {
      assert.ok(error, 'Error thrown when getting deleted user')
    }
  })
})
