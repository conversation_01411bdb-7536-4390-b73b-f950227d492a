// // For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, virtual } from '@feathersjs/schema'
import { Type, getValidator, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'

import type { HookContext } from '../../declarations'
import { dataValidator, queryValidator } from '../../validators'
import { userSchema } from '../users/users.schema'
import type { OrganizersService } from './organizers.class'

// Main data model schema
export const organizerSchema = Type.Object(
  {
    id: Type.Number(),
    userId: Type.Number(),
    name: Type.String(),
    bankAccount: Type.Optional(Type.String()),
    taxId: Type.Optional(Type.String()),
    contactPerson: Type.Optional(Type.String()),
    about: Type.Optional(Type.String()),
    isActive: Type.Optional(Type.Boolean()),
    createdAt: Type.Optional(Type.String({ format: 'date-time' })),
    updatedAt: Type.Optional(Type.String({ format: 'date-time' })),
    deletedAt: Type.Optional(Type.String({ format: 'date-time' })),
    createdBy: Type.Optional(Type.Number()),
    updatedBy: Type.Optional(Type.Number()),
    deletedBy: Type.Optional(Type.Number()),
    user: Type.Optional(Type.Ref(userSchema)),
    legacyId: Type.Optional(Type.Number()),
    address: Type.Optional(Type.String()),
    zipcode: Type.Optional(Type.String()),
    city: Type.Optional(Type.String()),
    country: Type.Optional(Type.String()),
    phone: Type.Optional(Type.String()),
    latitude: Type.Optional(Type.Number()),
    longitude: Type.Optional(Type.Number()),
    avatar: Type.Optional(Type.String())
  },
  { $id: 'Organizer', additionalProperties: false }
)
export type Organizer = Static<typeof organizerSchema>
export const organizerValidator = getValidator(organizerSchema, dataValidator)
export const organizerResolver = resolve<Organizer, HookContext<OrganizersService>>({
  user: virtual(async (organizer, context) => {
    // Associate the organizer with the user
    return context.app.service('users').get(organizer.userId)
  })
})

export const organizerExternalResolver = resolve<Organizer, HookContext<OrganizersService>>({})

// Schema for creating new entries
export const organizerDataSchema = Type.Pick(
  organizerSchema,
  [
    'userId', 'name', 'bankAccount', 'taxId', 'contactPerson', 'about', 'isActive', 'legacyId',
    'address', 'zipcode', 'city', 'country', 'phone', 'latitude', 'longitude', 'avatar'
  ],
  {
    $id: 'OrganizerData'
  }
)
export type OrganizerData = Static<typeof organizerDataSchema>
export const organizerDataValidator = getValidator(organizerDataSchema, dataValidator)
export const organizerDataResolver = resolve<Organizer, HookContext<OrganizersService>>({
  createdAt: async () => {
    return new Date().toISOString()
  },
  updatedAt: async () => {
    return new Date().toISOString()
  }
})

// Schema for updating existing entries
export const organizerPatchSchema = Type.Partial(
  Type.Pick(
    organizerSchema,
    ['name', 'bankAccount', 'taxId', 'contactPerson', 'about', 'isActive', 'deletedBy', 'deletedAt', 'updatedBy', 'updatedAt']
  ),
  {
    $id: 'OrganizerPatch'
  }
)
export type OrganizerPatch = Static<typeof organizerPatchSchema>
export const organizerPatchValidator = getValidator(organizerPatchSchema, dataValidator)
export const organizerPatchResolver = resolve<Organizer, HookContext<OrganizersService>>({
  updatedAt: async () => {
    return new Date().toISOString()
  }
})

// Schema for allowed query properties
export const organizerQueryProperties = Type.Pick(organizerSchema, [
  'id',
  'userId',
  'name',
  'isActive',
  'legacyId' // Added legacyId for querying
])
export const organizerQuerySchema = Type.Intersect(
  [
    querySyntax(organizerQueryProperties),
    // Add additional query properties here
    Type.Object({}, { additionalProperties: false })
  ],
  { additionalProperties: false }
)
export type OrganizerQuery = Static<typeof organizerQuerySchema>
export const organizerQueryValidator = getValidator(organizerQuerySchema, queryValidator)
export const organizerQueryResolver = resolve<OrganizerQuery, HookContext<OrganizersService>>({})
