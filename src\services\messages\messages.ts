import { createSwaggerServiceOptions } from 'feathers-swagger';

// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import { authenticate } from '@feathersjs/authentication'
import { hooks as schemaHooks } from '@feathersjs/schema'

import type { Application } from '../../declarations'
import { logRuntime } from '../../hooks/log-runtime'
import { populateUserTracking, skipIfDeletedByHook } from '../../hooks/user-tracking'
import { MessagesService, getOptions } from './messages.class'
import {
  messageDataResolver,
  messageDataSchema,
  messageDataValidator,
  messageExternalResolver,
  messagePatchResolver,
  messagePatchSchema,
  messagePatchValidator,
  messageQueryResolver,
  messageQuerySchema,
  messageQueryValidator,
  messageResolver,
  messageSchema
} from './messages.schema'
import { messagesMethods, messagesPath } from './messages.shared'

export * from './messages.class'
export * from './messages.schema'

// A configure function that registers the service and its hooks via `app.configure`
export const messages = (app: Application) => {
  // Register our service on the Feathers application
  app.use(messagesPath, new MessagesService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: messagesMethods,
    // You can add additional custom events to be sent to clients here
    events: [],
    docs: createSwaggerServiceOptions({
    schemas: { messageDataSchema, messageQuerySchema, messageSchema, messagePatchSchema },
      docs: { 
        description: 'Messages service',
        securities: ['all'],
      }
  })
  })
  // Initialize hooks
  app.service(messagesPath).hooks({
    around: {
      all: [
        logRuntime,
        authenticate('jwt'),
        schemaHooks.resolveExternal(messageExternalResolver),
        schemaHooks.resolveResult(messageResolver)
      ]
    },
    before: {
      all: [
        skipIfDeletedByHook(),
        schemaHooks.validateQuery(messageQueryValidator),
        schemaHooks.resolveQuery(messageQueryResolver)
      ],
      find: [],
      get: [],
      create: [
        schemaHooks.validateData(messageDataValidator),
        schemaHooks.resolveData(messageDataResolver),
        populateUserTracking()
      ],
      patch: [
        schemaHooks.validateData(messagePatchValidator),
        schemaHooks.resolveData(messagePatchResolver),
        populateUserTracking()
      ],
      remove: [populateUserTracking()]
    },
    after: {
      all: []
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    [messagesPath]: MessagesService
  }
}
