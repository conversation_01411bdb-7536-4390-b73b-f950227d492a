import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('matches', table => {
    // Drop the old boolean completed column
    table.dropColumn('completed');
    
    // Add the new timestamp completedAt column
    table.timestamp('completedAt').nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('matches', table => {
    // Drop the new timestamp column
    table.dropColumn('completedAt');
    
    // Add back the old boolean column
    table.boolean('completed').nullable();
  });
}
