import { createSwaggerServiceOptions } from 'feathers-swagger'

// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import { authenticate } from '@feathersjs/authentication'
import { hooks as schemaHooks } from '@feathersjs/schema'

import type { Application } from '../../declarations'
import { populateUserTracking, skipIfDeletedByHook } from '../../hooks/user-tracking'
import { PlayersService, getOptions } from './players.class'
import {
  playerDataResolver,
  playerDataSchema,
  playerDataValidator,
  playerExternalResolver,
  playerPatchResolver,
  playerPatchSchema,
  playerPatchValidator,
  playerQueryResolver,
  playerQuerySchema,
  playerQueryValidator,
  playerResolver,
  playerSchema
} from './players.schema'
import { playersMethods, playersPath } from './players.shared'

export * from './players.class'
export * from './players.schema'

// A configure function that registers the service and its hooks via `app.configure`
export const players = (app: Application) => {
  // Register our service on the Feathers application
  app.use(playersPath, new PlayersService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: playersMethods,
    // You can add additional custom events to be sent to clients here
    events: [],
    docs: createSwaggerServiceOptions({
      schemas: { playerDataSchema, playerQuerySchema, playerSchema, playerPatchSchema },
      docs: {
        description: 'Players service',
        securities: ['all']
      }
    })
  })
  // Initialize hooks
  app.service(playersPath).hooks({
    around: {
      all: [
        authenticate('jwt'),
        schemaHooks.resolveExternal(playerExternalResolver),
        schemaHooks.resolveResult(playerResolver)
      ]
    },
    before: {
      all: [skipIfDeletedByHook(), schemaHooks.validateQuery(playerQueryValidator), schemaHooks.resolveQuery(playerQueryResolver)],
      find: [],
      get: [],
      create: [schemaHooks.validateData(playerDataValidator), schemaHooks.resolveData(playerDataResolver), populateUserTracking()],
      patch: [schemaHooks.validateData(playerPatchValidator), schemaHooks.resolveData(playerPatchResolver), populateUserTracking()],
      remove: [populateUserTracking()]
    },
    after: {
      all: []
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    [playersPath]: PlayersService
  }
}
