import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
    await knex.schema.alterTable('tournaments', table => {
        // Add legacyId column as nullable integer for mapping old tournament IDs
        table.integer('legacyId').nullable()
    })
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.alterTable('tournaments', table => {
        // Drop legacyId column
        table.dropColumn('legacyId')
    })
}
