import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('match_registrations', table => {
    // Rename existing columns
    table.renameColumn('ageCategory', 'ageDivision');
    table.renameColumn('equipmentCategory', 'styleDivision');

    // Add new column
    table.string('genderDivision').nullable().comment('Gender division the player is registering for');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('match_registrations', table => {
    // Rename columns back to original names
    table.renameColumn('ageDivision', 'ageCategory');
    table.renameColumn('styleDivision', 'equipmentCategory');

    // Drop the new column
    table.dropColumn('genderDivision');
  });
}
