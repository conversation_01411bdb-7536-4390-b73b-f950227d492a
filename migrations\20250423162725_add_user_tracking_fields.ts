import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Add user tracking fields to users table
  await knex.schema.alterTable('users', table => {
    // Users table doesn't need createdBy since it's self-referential
    table.timestamp('createdAt').defaultTo(knex.fn.now());
    table.timestamp('updatedAt').defaultTo(knex.fn.now());
    table.timestamp('deletedAt').nullable();
    table.integer('updatedBy').nullable().references('id').inTable('users');
    table.integer('deletedBy').nullable().references('id').inTable('users');
  });

  // Add user tracking fields to matches table
  await knex.schema.alterTable('matches', table => {
    // Add updatedAt and deletedAt (createdAt already exists)
    table.timestamp('updatedAt').defaultTo(knex.fn.now());
    table.timestamp('deletedAt').nullable();
    table.integer('createdBy').nullable().references('id').inTable('users');
    table.integer('updatedBy').nullable().references('id').inTable('users');
    table.integer('deletedBy').nullable().references('id').inTable('users');
  });

  // Add user tracking fields to messages table
  await knex.schema.alterTable('messages', table => {
    // Messages already has createdAt and userId (which is effectively createdBy)
    table.timestamp('updatedAt').defaultTo(knex.fn.now());
    table.timestamp('deletedAt').nullable();
    table.integer('updatedBy').nullable().references('id').inTable('users');
    table.integer('deletedBy').nullable().references('id').inTable('users');
  });

  // Add user tracking fields to equipment table
  await knex.schema.alterTable('equipment', table => {
    // Equipment already has createdAt, updatedAt, and deletedAt
    table.integer('createdBy').nullable().references('id').inTable('users');
    table.integer('updatedBy').nullable().references('id').inTable('users');
    table.integer('deletedBy').nullable().references('id').inTable('users');
  });

  // Add user tracking fields to players table
  await knex.schema.alterTable('players', table => {
    // Players already has createdAt, updatedAt, and deletedAt
    table.integer('createdBy').nullable().references('id').inTable('users');
    table.integer('updatedBy').nullable().references('id').inTable('users');
    table.integer('deletedBy').nullable().references('id').inTable('users');
  });

  // Add user tracking fields to match_registrations table
  await knex.schema.alterTable('match_registrations', table => {
    // match_registrations already has createdAt and updatedAt
    table.timestamp('deletedAt').nullable();
    table.integer('createdBy').nullable().references('id').inTable('users');
    table.integer('updatedBy').nullable().references('id').inTable('users');
    table.integer('deletedBy').nullable().references('id').inTable('users');
  });
}

export async function down(knex: Knex): Promise<void> {
  // Remove user tracking fields from match_registrations table
  await knex.schema.alterTable('match_registrations', table => {
    table.dropColumn('deletedAt');
    table.dropColumn('createdBy');
    table.dropColumn('updatedBy');
    table.dropColumn('deletedBy');
  });

  // Remove user tracking fields from players table
  await knex.schema.alterTable('players', table => {
    table.dropColumn('createdBy');
    table.dropColumn('updatedBy');
    table.dropColumn('deletedBy');
  });

  // Remove user tracking fields from equipment table
  await knex.schema.alterTable('equipment', table => {
    table.dropColumn('createdBy');
    table.dropColumn('updatedBy');
    table.dropColumn('deletedBy');
  });

  // Remove user tracking fields from messages table
  await knex.schema.alterTable('messages', table => {
    table.dropColumn('updatedAt');
    table.dropColumn('deletedAt');
    table.dropColumn('updatedBy');
    table.dropColumn('deletedBy');
  });

  // Remove user tracking fields from matches table
  await knex.schema.alterTable('matches', table => {
    table.dropColumn('updatedAt');
    table.dropColumn('deletedAt');
    table.dropColumn('createdBy');
    table.dropColumn('updatedBy');
    table.dropColumn('deletedBy');
  });

  // Remove user tracking fields from users table
  await knex.schema.alterTable('users', table => {
    table.dropColumn('createdAt');
    table.dropColumn('updatedAt');
    table.dropColumn('deletedAt');
    table.dropColumn('updatedBy');
    table.dropColumn('deletedBy');
  });
}
