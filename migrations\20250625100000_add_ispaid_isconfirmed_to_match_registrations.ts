import { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
    await knex.schema.alterTable('match_registrations', (table) => {
        table.boolean('isPaid').nullable()
        table.boolean('isConfirmed').nullable()
    })
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.alterTable('match_registrations', (table) => {
        table.dropColumn('isPaid')
        table.dropColumn('isConfirmed')
    })
}
