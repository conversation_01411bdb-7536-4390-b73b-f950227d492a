// For more information about this file see https://dove.feathersjs.com/guides/cli/application.html#configure-functions
import type { Application } from '../declarations'
import { club } from './clubs/clubs'
import { email } from './email/email';
import { equipment } from './equipment/equipment'
import { federations } from './federations/federations'
import { matchFormats } from './match-formats/match-formats'
import { matchRegistrations } from './match-registrations/match-registrations'
import { matchResults } from './match-results/match-results'
import { matches } from './matches/matches'
import { messages } from './messages/messages'
import { organizers } from './organizers/organizers'
import { players } from './players/players'
import { tournaments } from './tournaments/tournaments'
import { userMe } from './user-me/user-me'
import { user } from './users/users'

export const services = (app: Application) => {
  app.configure(email)
  app.configure(userMe)
  app.configure(club)
  app.configure(matchFormats)
  app.configure(organizers)
  app.configure(tournaments)
  app.configure(matchResults)
  app.configure(equipment)
  app.configure(players)
  app.configure(messages)
  app.configure(matches)
  app.configure(user)
  app.configure(matchRegistrations)
  app.configure(federations)

}
