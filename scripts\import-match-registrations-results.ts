import fs from 'fs';
import path from 'path';

import { parse } from 'csv-parse';

import { app } from '../src/app';
import type { MatchRegistrationData } from '../src/services/match-registrations/match-registrations.schema';
import type { MatchResultData } from '../src/services/match-results/match-results.schema';

// Define an interface for Feathers error type
interface FeathersError extends Error {
  code: number;
  className: string;
  data?: any[];
}

/**
 * Convert string to boolean
 */
const parseBool = (value: string | undefined): boolean | undefined => {
  if (value === undefined || value === '' || value === '\\N') return undefined;
  if (value === '0') return false;
  if (value === '1') return true;
  return undefined;
};

/**
 * Parse string fields, handling special null values
 */
const parseString = (value: string | undefined): string | undefined => {
  if (!value || value === '\\N' || value === 'NULL' || value === 'null' || value === '') {
    return undefined;
  }
  return value.trim();
};

/**
 * Parse number from string
 */
const parseNumber = (value: string | undefined): number | undefined => {
  if (!value || value === '\\N' || value === '-1' || value === '') return undefined;

  const num = Number(value);
  return !isNaN(num) ? num : undefined;
};

/**
 * Parse JSON field from string 
 */
const parseJsonField = (value: string | undefined): string | undefined => {
  if (!value || value === '\\N' || value === '-1') return undefined;

  try {
    // Just validate that it's valid JSON, but return the original string
    JSON.parse(value);
    return value;
  } catch (error) {
    console.error(`Error parsing JSON value: ${value}`, error);
    return undefined;
  }
};

/**
 * Parse timestamp from DD.MM.YYYY HH:MM format
 */
const parseTimestamp = (value: string | undefined): string | undefined => {
  if (!value || value === '\\N') return undefined;

  try {
    // Handle format DD.MM.YYYY HH:MM
    if (value.includes('.') && value.includes(' ')) {
      const [datePart, timePart] = value.split(' ');
      const [day, month, year] = datePart.split('.');
      return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T${timePart}:00.000Z`;
    }

    // Handle format DD.MM.YYYY
    if (value.includes('.')) {
      const [day, month, year] = value.split('.');
      return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T00:00:00.000Z`;
    }

    // Try to parse as ISO string
    const date = new Date(value);
    if (!isNaN(date.getTime())) {
      return date.toISOString();
    }
  } catch (error) {
    console.error(`Error parsing timestamp: ${value}`, error);
  }

  return undefined;
};

/**
 * Resolve match ID by legacy ID
 */
async function resolveMatchId(legacyCompetitionId: string | undefined): Promise<number | undefined> {
  if (!legacyCompetitionId || legacyCompetitionId === '\\N') return undefined;

  try {
    const matchesService = app.service('matches');
    const found = await matchesService.find({
      query: { legacyId: parseNumber(legacyCompetitionId) } as any,
      paginate: false
    });

    if (Array.isArray(found) && found.length > 0) {
      return found[0].id;
    }
  } catch (error) {
    console.error(`Error finding match with legacyId ${legacyCompetitionId}:`, error);
  }

  return undefined;
}

/**
 * Resolve player ID by legacy ID
 */
async function resolvePlayerId(legacyPlayerId: string | undefined): Promise<number | undefined> {
  if (!legacyPlayerId || legacyPlayerId === '\\N') return undefined;

  try {
    const playersService = app.service('players');
    const found = await playersService.find({
      query: { legacyId: parseNumber(legacyPlayerId) } as any,
      paginate: false
    });

    if (Array.isArray(found) && found.length > 0) {
      return found[0].id;
    }
  } catch (error) {
    console.error(`Error finding player with legacyId ${legacyPlayerId}:`, error);
  }

  return undefined;
}

/**
 * Resolve equipment ID by legacy ID
 */
async function resolveEquipmentId(legacyBowId: string | undefined): Promise<number | undefined> {
  if (!legacyBowId || legacyBowId === '\\N') return undefined;

  try {
    const equipmentService = app.service('equipment');
    const found = await equipmentService.find({
      query: { legacyId: parseNumber(legacyBowId) } as any,
      paginate: false
    });

    if (Array.isArray(found) && found.length > 0) {
      return found[0].id;
    }
  } catch (error) {
    console.error(`Error finding equipment with legacyId ${legacyBowId}:`, error);
  }

  return undefined;
}

/**
 * Main import function for match registrations and results from CSV
 */
async function importMatchRegistrationsResults(csvFilePath: string) {
  const matchRegistrations = app.service('match-registrations');
  const matchResults = app.service('match-results');

  // Ensure the path is absolute
  const filePath = path.isAbsolute(csvFilePath)
    ? csvFilePath
    : path.join(process.cwd(), csvFilePath);

  console.log(`Importing match registrations and results from ${filePath}`);

  // Read first line to detect delimiter
  const firstLine = fs.readFileSync(filePath, 'utf8').split('\n')[0];
  const delimiter = firstLine.includes('\t') ? '\t' : (firstLine.includes(';') ? ';' : ',');
  console.log(`Detected delimiter: ${delimiter === '\t' ? 'TAB' : delimiter}`);

  const parser = fs
    .createReadStream(filePath)
    .pipe(parse({
      delimiter,
      columns: true,
      skip_empty_lines: true,
      relax_quotes: true,
      skip_records_with_empty_values: false
    }));

  let recordCount = 0;
  let registrationImportCount = 0;
  let resultImportCount = 0;
  let errorCount = 0;
  const errorLogPath = path.join(process.cwd(), 'import-match-registrations-results-errors.txt');
  fs.writeFileSync(errorLogPath, '');

  for await (const record of parser) {
    recordCount++;

    // Debug: Show first record structure
    if (recordCount === 1) {
      console.log('First record structure:', Object.keys(record));
      console.log('First record data:', record);
    }

    // Skip completely empty rows
    if (Object.values(record).every(v => !v || v === '')) {
      continue;
    }

    try {
      // Resolve foreign keys
      const matchId = await resolveMatchId(record.competition_id);
      const playerId = await resolvePlayerId(record.player_id);
      const equipmentId = await resolveEquipmentId(record.bow_id);

      if (!matchId) {
        console.log(`Skipping record #${recordCount} - match not found for legacyId: ${record.competition_id}`);
        continue;
      }

      if (!playerId) {
        console.log(`Skipping record #${recordCount} - player not found for legacyId: ${record.player_id}`);
        continue;
      }

      // Create match registration
      const registrationData: MatchRegistrationData = {
        matchId,
        playerId,
        styleDivision: parseString(record.equipment_category),
        ageDivision: parseString(record.age_category),
        equipmentId,
        isPaid: parseBool(record.payed),
        isConfirmed: parseBool(record.confirmed)
      };

      const registration = await matchRegistrations.create(registrationData);
      registrationImportCount++;
      console.log(`Created registration #${registrationImportCount} for match ${matchId}, player ${playerId}`);

      // Create match result if points data exists
      const points = parseNumber(record.points);
      const maxPoints = parseNumber(record.max_points);
      const place = parseNumber(record.place);
      const scores = parseJsonField(record.points_json);

      if (points !== undefined && maxPoints !== undefined) {
        const resultData: MatchResultData = {
          matchId,
          playerId,
          points,
          maxPoints,
          place,
          scores
        };

        await matchResults.create(resultData);
        resultImportCount++;
        console.log(`Created result for match ${matchId}, player ${playerId}: ${points}/${maxPoints} points, place ${place || 'N/A'}`);
      }

    } catch (error) {
      errorCount++;
      const errorMessage = `Error processing record #${recordCount}: ${error instanceof Error ? error.message : String(error)}\nRecord: ${JSON.stringify(record)}\n\n`;
      console.error(errorMessage);
      fs.appendFileSync(errorLogPath, errorMessage);
    }
  }

  console.log(`\nImport completed:`);
  console.log(`- Total records processed: ${recordCount}`);
  console.log(`- Registrations imported: ${registrationImportCount}`);
  console.log(`- Results imported: ${resultImportCount}`);
  console.log(`- Errors: ${errorCount}`);

  if (errorCount > 0) {
    console.log(`- Error details saved to: ${errorLogPath}`);
  }
}

// Run the import if this script is executed directly
if (require.main === module) {
  const csvFile = process.argv[2];
  if (!csvFile) {
    console.error('Error: CSV file path is required');
    console.error('Usage: pnpm exec ts-node scripts/import-match-registrations-results.ts <path/to/csv/file>');
    process.exit(1);
  }

  importMatchRegistrationsResults(csvFile)
    .then(() => {
      console.log('Import completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Import failed:', error);
      process.exit(1);
    });
}

export { importMatchRegistrationsResults };
