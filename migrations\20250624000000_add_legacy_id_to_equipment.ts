import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
    await knex.schema.alterTable('equipment', (table) => {
        table.integer('legacyId').nullable()
        table.index('legacyId')
    })
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.alterTable('equipment', (table) => {
        table.dropIndex('legacyId')
        table.dropColumn('legacyId')
    })
}
