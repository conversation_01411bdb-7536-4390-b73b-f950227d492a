// For more information about this file see https://dove.feathersjs.com/guides/cli/service.test.html
import assert from 'assert'
import { app } from '../../../src/app'
import type { ClubData } from '../../../src/services/clubs/clubs.class'

describe('clubs service', () => {
  const service = app.service('clubs')
  let clubId: number

  it('registered the service', () => {
    assert.ok(service, 'Registered the service')
  })

  it('creates a club', async () => {
    const clubData: ClubData = {
      text: 'Test Club'
    }

    const club = await service.create(clubData)

    assert.ok(club, 'Created a club')
    assert.ok(club.id, 'Club has an id')
    assert.equal(club.text, clubData.text, 'Sets the text')

    // Save club ID for later tests
    clubId = club.id
  })

  it('gets a club', async () => {
    const club = await service.get(clubId)

    assert.ok(club, 'Got the club')
    assert.equal(club.id, clubId, 'Got the correct club')
    assert.equal(club.text, 'Test Club', 'Text matches')
  })

  it('finds clubs with pagination', async () => {
    const result = await service.find({
      query: {
        $limit: 10
      }
    })

    assert.ok(result.data, 'Returns data array')
    assert.ok(result.total >= 1, 'Returns at least one club')
    assert.ok(result.limit === 10, 'Returns specified limit')
  })

  it('updates a club', async () => {
    const updatedData = {
      text: 'Updated Test Club'
    }

    const updated = await service.patch(clubId, updatedData)

    assert.equal(updated.text, updatedData.text, 'Updated the text')
    assert.equal(updated.id, clubId, 'ID remained the same')
  })

  it('removes a club', async () => {
    const removed = await service.remove(clubId)

    assert.ok(removed, 'Removed the club')
    assert.equal(removed.id, clubId, 'Removed the correct club')

    try {
      await service.get(clubId)
      assert.fail('Should have thrown an error for deleted club')
    } catch (error) {
      assert.ok(error, 'Error thrown when getting deleted club')
    }
  })
})
