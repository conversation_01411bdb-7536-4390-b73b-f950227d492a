/**
 * <PERSON><PERSON><PERSON> to create the test database if it doesn't exist
 */
const { Client } = require('pg');

async function setupTestDatabase() {
  // Connect to the default postgres database
  const client = new Client({
    user: 'postgres',
    host: 'localhost',
    password: 'cruARCHERY7*',
    port: 5432,
    database: 'postgres'
  });

  try {
    await client.connect();
    
    // Check if the test database exists
    const res = await client.query(
      "SELECT 1 FROM pg_database WHERE datname = 'archery_test'"
    );
    
    // If the database doesn't exist, create it
    if (res.rowCount === 0) {
      console.log('Creating archery_test database...');
      await client.query('CREATE DATABASE archery_test');
      console.log('Test database created successfully');
    } else {
      console.log('Test database already exists');
    }
  } catch (err) {
    console.error('Error setting up test database:', err);
    process.exit(1);
  } finally {
    await client.end();
  }
}

setupTestDatabase();
