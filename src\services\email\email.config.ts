// src/services/email/email.config.ts
export interface EmailConfig {
  provider?: 'smtp' | 'mock' | 'test';
  defaultFrom?: string;
  host?: string;
  port?: number;
  secure?: boolean;
  auth?: {
    user: string;
    pass: string;
  };
  smtp?: {
    host: string;
    port: number;
    secure: boolean;
    auth: {
      user: string;
      pass: string;
    };
  };
}

export const validateEmailConfig = (config: any): EmailConfig => {
  if (!config) {
    return {};
  }
  
  if (config?.provider === 'smtp') {
    // Check both direct config and nested smtp config
    const smtpConfig = config.smtp || config;
    if (!smtpConfig.host || !smtpConfig.auth?.user || !smtpConfig.auth?.pass) {
      throw new Error('SMTP provider requires host, auth.user, and auth.pass configuration');
    }
  }
  
  return config;
};
