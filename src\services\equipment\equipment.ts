import { createSwaggerServiceOptions } from 'feathers-swagger'

// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import { authenticate } from '@feathersjs/authentication'
import { hooks as schemaHooks } from '@feathersjs/schema'

import type { Application } from '../../declarations'
import { populateUserTracking, skipIfDeletedByHook } from '../../hooks/user-tracking'
import { EquipmentService, getOptions } from './equipment.class'
import {
  equipmentDataResolver,
  equipmentDataSchema,
  equipmentDataValidator,
  equipmentExternalResolver,
  equipmentPatchResolver,
  equipmentPatchSchema,
  equipmentPatchValidator,
  equipmentQueryResolver,
  equipmentQuerySchema,
  equipmentQueryValidator,
  equipmentResolver,
  equipmentSchema
} from './equipment.schema'
import { equipmentMethods, equipmentPath } from './equipment.shared'

export * from './equipment.class'
export * from './equipment.schema'

// A configure function that registers the service and its hooks via `app.configure`
export const equipment = (app: Application) => {
  // Register our service on the Feathers application
  app.use(equipmentPath, new EquipmentService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: equipmentMethods,
    // You can add additional custom events to be sent to clients here
    events: [],
    docs: createSwaggerServiceOptions({
      schemas: { equipmentDataSchema, equipmentQuerySchema, equipmentSchema, equipmentPatchSchema },
      docs: {
        description: 'Equipment service',
        securities: ['all']
      }
    })
  })
  // Initialize hooks
  app.service(equipmentPath).hooks({
    around: {
      all: [
        authenticate('jwt'),
        schemaHooks.resolveExternal(equipmentExternalResolver),
        schemaHooks.resolveResult(equipmentResolver)
      ]
    },
    before: {
      all: [
        skipIfDeletedByHook(),
        schemaHooks.validateQuery(equipmentQueryValidator),
        schemaHooks.resolveQuery(equipmentQueryResolver)
      ],
      find: [],
      get: [],
      create: [
        schemaHooks.validateData(equipmentDataValidator),
        schemaHooks.resolveData(equipmentDataResolver),
        populateUserTracking()
      ],
      patch: [
        schemaHooks.validateData(equipmentPatchValidator),
        schemaHooks.resolveData(equipmentPatchResolver),
        populateUserTracking()
      ],
      remove: [populateUserTracking()]
    },
    after: {
      all: []
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    [equipmentPath]: EquipmentService
  }
}
