// For more information about this file see https://dove.feathersjs.com/guides/cli/service.test.html
import assert from 'assert'

import { app } from '../../../src/app'
import type { EquipmentData } from '../../../src/services/equipment/equipment.class'

describe('equipment service', () => {
  const service = app.service('equipment')
  const usersService = app.service('users')
  const playersService = app.service('players')
  const authService = app.service('authentication')

  let userId: number
  let playerId: number
  let equipmentId: number
  let accessToken: string
  let userParams: any

  // Create a test user and player before running equipment tests
  before(async () => {
    // Create test user
    const testUser = await usersService.create({
      email: `test-equipment-${Date.now()}@example.com`,
      password: 'supersecret'
    })

    userId = testUser.id

    // Authenticate the user
    const authResult = await authService.create({
      strategy: 'local',
      email: testUser.email,
      password: 'supersecret'
    })

    accessToken = authResult.accessToken

    // Create params with authentication
    userParams = {
      provider: 'rest',
      authentication: {
        strategy: 'jwt',
        accessToken
      },
      user: testUser
    }

    // Create a player profile for the user
    const player = await playersService.create({
      userId: userId,
      firstname: 'Equipment',
      lastname: 'Tester',
      isActive: true
    }, userParams)

    playerId = player.id
  })

  // Clean up test data after tests
  after(async () => {
    try {
      // Remove player first (to avoid foreign key constraints)
      await playersService.remove(playerId, userParams)
      // Then remove user
      await usersService.remove(userId)
    } catch (error) {
      console.error('Error cleaning up test data:', error)
    }
  })

  it('registered the service', () => {
    assert.ok(service, 'Registered the service')
  })

  it('requires authentication', async () => {
    try {
      await service.find()
      assert.fail('Should not allow unauthenticated access')
    } catch (error: any) {
      assert.ok(error, 'Returns an error for unauthenticated access')
    }
  })

  it('creates equipment with user tracking fields', async () => {
    const equipmentData: EquipmentData = {
      name: 'Test Bow',
      category: 'recurve',
      equipmentClass: 'bow',
      type: 'competition',
      isActive: true,
      playerId: playerId,
      isDefault: true,
      configuration: {
        limbType: 'carbon',
        drawWeight: 40
      }
    }

    const equipment = await service.create(equipmentData, userParams)

    assert.ok(equipment, 'Created equipment')
    assert.ok(equipment.id, 'Equipment has an id')
    assert.equal(equipment.name, equipmentData.name, 'Sets the name')
    assert.equal(equipment.category, equipmentData.category, 'Sets the category')
    assert.equal(equipment.playerId, playerId, 'Associates with the player')

    // Verify user tracking fields
    assert.equal(equipment.createdBy, userId, 'Sets createdBy to current user ID')
    assert.equal(equipment.updatedBy, userId, 'Sets updatedBy to current user ID')
    assert.ok(equipment.createdAt, 'Sets createdAt timestamp')
    assert.ok(equipment.updatedAt, 'Sets updatedAt timestamp')

    // Save equipment ID for later tests
    equipmentId = equipment.id
  })

  it('gets equipment', async () => {
    const equipment = await service.get(equipmentId, userParams)

    assert.ok(equipment, 'Got the equipment')
    assert.equal(equipment.id, equipmentId, 'Got the correct equipment')
    assert.equal(equipment.playerId, playerId, 'Player association is maintained')
  })

  it('finds equipment with pagination', async () => {
    const result = await service.find({
      ...userParams,
      query: {
        $limit: 10
      }
    })

    assert.ok(result.data, 'Returns data array')
    assert.ok(result.total >= 1, 'Returns at least one equipment')
    assert.ok(result.limit === 10, 'Returns specified limit')
  })

  it('updates equipment and tracks the user who made the update', async () => {
    const updatedData = {
      name: 'Updated Test Bow',
      configuration: {
        limbType: 'carbon',
        drawWeight: 42
      }
    }

    const updated = await service.patch(equipmentId, updatedData, userParams)

    assert.equal(updated.name, updatedData.name, 'Updated the name')
    assert.equal(updated.id, equipmentId, 'ID remained the same')
    assert.equal(updated.playerId, playerId, 'Player association remained the same')

    // Verify user tracking fields for update
    assert.equal(updated.updatedBy, userId, 'Sets updatedBy to current user ID')
    assert.ok(updated.updatedAt, 'Updates the updatedAt timestamp')
  })

  it('removes equipment and tracks who deleted it', async () => {
    const removed = await service.remove(equipmentId, userParams)

    assert.ok(removed, 'Removed the equipment')
    assert.equal(removed.id, equipmentId, 'Removed the correct equipment')

    // Verify user tracking fields for deletion
    assert.equal(removed.deletedBy, userId, 'Sets deletedBy to current user ID')
    assert.ok(removed.deletedAt, 'Sets deletedAt timestamp')

    try {
      await service.get(equipmentId, userParams)
      assert.fail('Should have thrown an error for deleted equipment')
    } catch (error) {
      assert.ok(error, 'Error thrown when getting deleted equipment')
    }
  })
})
