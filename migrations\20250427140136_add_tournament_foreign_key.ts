import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
    const hasColumn = await knex.schema.hasColumn('matches', 'tournamentId');

    // First, drop the column if it exists
    if (hasColumn) {
      await knex.schema.alterTable('matches', table => {
        table.dropColumn('tournamentId')
      })
    }
  
    // Add the tournamentId column with a foreign key constraint
    await knex.schema.alterTable('matches', table => {
      table.integer('tournamentId').nullable().references('id').inTable('tournaments')

  
      // Add an index for faster lookups
      table.index(['tournamentId'], 'idx_matches_tournament_id')

})
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('matches', table => {
    // Drop foreign key constraint
    table.dropForeign(['tournamentId']);
    table.dropIndex(['tournamentId'], 'idx_matches_tournament_id');
  });
}

