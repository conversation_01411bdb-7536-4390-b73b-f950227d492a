// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'

import type { ClientApplication } from '../../client'
import type { Player, PlayerData, PlayerPatch, PlayerQuery, PlayersService } from './players.class'

export type { Player, PlayerData, PlayerPatch, PlayerQuery }

export type PlayersClientService = Pick<PlayersService<Params<PlayerQuery>>, (typeof playersMethods)[number]>

export const playersPath = 'players'

export const playersMethods: Array<keyof PlayersService> = ['find', 'get', 'create', 'patch', 'remove']

export const playersClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(playersPath, connection.service(playersPath), {
    methods: playersMethods
  })
}

// Add this service to the client service type index
declare module '../../client' {
  interface ServiceTypes {
    [playersPath]: PlayersClientService
  }
}
