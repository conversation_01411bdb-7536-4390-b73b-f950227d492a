import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
    await knex.schema.createTable('competition_formats', (table) => {
        table.increments('id').primary()
        table.integer('legacyId').index()
        table.boolean('active').notNullable().defaultTo(true)
        table.string('name').notNullable()
        table.integer('numberOfShots').nullable()
        table.string('typesOfTargets').nullable()
        table.integer('targets').nullable()
        table.jsonb('scoring').nullable().comment('JSON array of scoring values')
        table.string('matchType').nullable()
        table.integer('federationId').nullable().references('id').inTable('federations')

        // User tracking and soft deletes
        table.timestamp('createdAt', { useTz: true }).defaultTo(knex.fn.now())
        table.timestamp('updatedAt', { useTz: true }).defaultTo(knex.fn.now())
        table.timestamp('deletedAt', { useTz: true }).nullable()
        table.integer('createdBy').nullable().references('id').inTable('users')
        table.integer('updatedBy').nullable().references('id').inTable('users')
        table.integer('deletedBy').nullable().references('id').inTable('users')

        // Indexes
        table.index(['federationId'], 'idx_competition_formats_federation_id')
        table.index(['active'], 'idx_competition_formats_active')
        table.index(['matchType'], 'idx_competition_formats_match_type')
    })
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.dropTable('competition_formats')
}
