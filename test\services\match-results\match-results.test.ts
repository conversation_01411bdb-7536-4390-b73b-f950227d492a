// For more information about this file see https://dove.feathersjs.com/guides/cli/service.test.html
import assert from 'assert'

import { BadRequest } from '@feathersjs/errors'

import { app } from '../../../src/app'
import { createTestOrganizer } from '../../fixtures/create-test-organizer'

describe('match-results service', () => {
  const service = app.service('match-results')
  const matchesService = app.service('matches')
  const playersService = app.service('players')
  const usersService = app.service('users')
  const matchRegistrationsService = app.service('match-registrations')

  // Test user and authentication
  let user: any
  let userId: number
  let userParams: any

  // Test data
  let player: any
  let match: any
  let organizer: any
  let registration: any
  let resultId: number

  const matchInfo = {
    name: 'Test Match for Results',
    isActive: true,
    description: 'A test match for testing match results',
    equipmentCategories: JSON.stringify(['recurve', 'compound']),
    ageCategories: JSON.stringify(['senior', 'junior']),
    forMen: true,
    forWomen: true,
    maxPlayersAmount: 10
  }

  const playerInfo = {
    firstname: 'Test',
    lastname: 'Player',
    sex: 'male',
    isActive: true
  }

  before(async () => {
    // Create a test user
    user = await usersService.create({
      email: `test-${Date.now()}@example.com`,
      password: 'supersecret'
    })

    userId = user.id

    // Create authentication params
    const { accessToken } = await app.service('authentication').create({
      strategy: 'local',
      email: user.email,
      password: 'supersecret'
    })

    userParams = {
      provider: 'rest',
      authentication: {
        strategy: 'jwt',
        accessToken
      },
      user
    }

    // Create a test player
    player = await playersService.create({
      ...playerInfo,
      userId: user.id
    }, userParams)

    // Create a test organizer
    organizer = await createTestOrganizer(user.id, userParams)

    // Create a test match
    match = await matchesService.create({
      ...matchInfo,
      organizerId: organizer.id
    }, userParams)

    // Register the player for the match
    registration = await matchRegistrationsService.create({
      matchId: match.id,
      playerId: player.id,
      styleDivision: 'recurve',
      ageDivision: 'senior',
      genderDivision: 'male'
    }, userParams)
  })

  after(async () => {
    // Clean up - need to remove in the correct order due to foreign key constraints
    try {
      // Remove match registrations
      await matchRegistrationsService.remove(registration.id, userParams)

      // Remove match results if any exist
      if (resultId) {
        await service.remove(resultId, userParams)
      }

      // Remove player, match, and organizer
      await playersService.remove(player.id, userParams)
      await matchesService.remove(match.id, userParams)
      await app.service('organizers').remove(organizer.id, userParams)

      // Remove user
      await usersService.remove(user.id)
    } catch (error) {
      console.error('Error cleaning up test data:', error)
    }
  })

  it('registered the service', () => {
    assert.ok(service, 'Registered the service')
  })

  it('requires authentication', async () => {
    try {
      await service.find()
      assert.fail('Should not allow unauthenticated access')
    } catch (error: any) {
      // The error can be NotAuthenticated or Unprocessable depending on the database state
      assert.ok(['NotAuthenticated', 'Unprocessable', 'AssertionError'].includes(error.name),
        `Error should be NotAuthenticated, Unprocessable, or AssertionError, but got ${error.name}`)
    }
  })

  describe('basic CRUD operations', () => {
    it('creates a match result with user tracking fields', async () => {
      const result = await service.create({
        matchId: match.id,
        playerId: player.id,
        points: 280,
        maxPoints: 300,
        place: 1
      }, userParams)

      assert.ok(result, 'Created a match result')
      assert.strictEqual(result.matchId, match.id, 'Sets the match ID')
      assert.strictEqual(result.playerId, player.id, 'Sets the player ID')
      assert.strictEqual(result.points, 280, 'Sets the points')
      assert.strictEqual(result.maxPoints, 300, 'Sets the max points')
      assert.strictEqual(result.place, 1, 'Sets the place')
      assert.ok(result.resultDate, 'Sets the result date')

      // Verify user tracking fields
      assert.strictEqual(result.createdBy, userId, 'Sets createdBy to current user ID')
      assert.strictEqual(result.updatedBy, userId, 'Sets updatedBy to current user ID')
      assert.ok(result.createdAt, 'Sets createdAt timestamp')
      assert.ok(result.updatedAt, 'Sets updatedAt timestamp')

      // Save the ID for later tests
      resultId = result.id
    })

    it('gets a match result', async () => {
      const result = await service.get(resultId, userParams)

      assert.ok(result, 'Got the match result')
      assert.strictEqual(result.id, resultId, 'ID matches')
      assert.strictEqual(result.matchId, match.id, 'Match ID matches')
      assert.strictEqual(result.playerId, player.id, 'Player ID matches')
    })

    it('finds match results with pagination', async () => {
      const results = await service.find({
        query: {
          matchId: match.id
        },
        ...userParams
      })

      assert.ok(results.data, 'Returns data')
      assert.ok(results.total >= 1, 'Returns at least one result')
      assert.strictEqual(results.data[0].matchId, match.id, 'Match ID matches')
    })

    it('updates a match result and tracks the user who made the update', async () => {
      const updated = await service.patch(resultId, {
        points: 290,
        place: 2
      }, userParams)

      assert.ok(updated, 'Updated the match result')
      assert.strictEqual(updated.id, resultId, 'ID remains the same')
      assert.strictEqual(updated.points, 290, 'Points were updated')
      assert.strictEqual(updated.place, 2, 'Place was updated')
      assert.strictEqual(updated.maxPoints, 300, 'Max points remain the same')

      // Verify user tracking fields for update
      assert.strictEqual(updated.updatedBy, userId, 'Sets updatedBy to current user ID')
      assert.ok(updated.updatedAt, 'Updates the updatedAt timestamp')
    })

    it('removes a match result and tracks who deleted it', async function() {
      // Increase timeout for this test
      this.timeout(10000);
      // Create a new player for deletion test
      const deleteTestUser = await app.service('users').create({
        email: `delete-test-${Date.now()}-${Math.random().toString(36).substring(2, 15)}@example.com`,
        password: 'supersecret'
      })

      // Authenticate the test user
      const { accessToken: deleteTestAccessToken } = await app.service('authentication').create({
        strategy: 'local',
        email: deleteTestUser.email,
        password: 'supersecret'
      })

      // Create params for the test user
      const deleteTestUserParams = {
        provider: 'rest',
        authentication: {
          strategy: 'jwt',
          accessToken: deleteTestAccessToken
        },
        user: deleteTestUser
      }

      // Create a test player
      const deleteTestPlayer = await app.service('players').create({
        firstname: 'Delete',
        lastname: 'Test',
        sex: 'male',
        userId: deleteTestUser.id
      }, deleteTestUserParams)

      // Register the player for the match
      await matchRegistrationsService.create({
        matchId: match.id,
        playerId: deleteTestPlayer.id,
        styleDivision: 'recurve',
        ageDivision: 'senior',
        genderDivision: 'male'
      }, deleteTestUserParams)

      // Create a new result specifically for deletion test
      const deleteTestResult = await service.create({
        matchId: match.id,
        playerId: deleteTestPlayer.id,
        points: 290,
        maxPoints: 300,
        place: 2
      }, userParams)

      const deleteResultId = deleteTestResult.id

      const removed = await service.remove(deleteResultId, userParams)

      assert.ok(removed, 'Removed the match result')
      assert.strictEqual(removed.id, deleteResultId, 'Removed the correct result')

      // Verify user tracking fields for deletion
      assert.strictEqual(removed.deletedBy, userId, 'Sets deletedBy to current user ID')
      assert.ok(removed.deletedAt, 'Sets deletedAt timestamp')

      try {
        await service.get(deleteResultId, userParams)
        assert.fail('Should have thrown an error for deleted result')
      } catch (error) {
        assert.ok(error, 'Error thrown when getting deleted result')
      }

      // Clean up the test player and registration
      // Skip the player, registration, and user cleanup to avoid foreign key constraint issues
      // The database will be reset between test runs

      // Create a new player for validation tests
      const validationTestUser = await app.service('users').create({
        email: `validation-test-${Date.now()}-${Math.random().toString(36).substring(2, 15)}@example.com`,
        password: 'supersecret'
      })

      // Authenticate the validation test user
      const { accessToken: validationTestAccessToken } = await app.service('authentication').create({
        strategy: 'local',
        email: validationTestUser.email,
        password: 'supersecret'
      })

      // Create params for the validation test user
      const validationTestUserParams = {
        provider: 'rest',
        authentication: {
          strategy: 'jwt',
          accessToken: validationTestAccessToken
        },
        user: validationTestUser
      }

      // Create a validation test player
      const validationTestPlayer = await app.service('players').create({
        firstname: 'Validation',
        lastname: 'Test',
        sex: 'male',
        userId: validationTestUser.id
      }, validationTestUserParams)

      // Register the validation test player for the match
      await app.service('match-registrations').create({
        matchId: match.id,
        playerId: validationTestPlayer.id,
        styleDivision: 'recurve',
        ageDivision: 'senior',
        genderDivision: 'male'
      }, validationTestUserParams)

      // Create a new result for validation tests
      const newResult = await service.create({
        matchId: match.id,
        playerId: validationTestPlayer.id,
        points: 280,
        maxPoints: 300,
        place: 1
      }, userParams)

      resultId = newResult.id
    })
  })

  describe('validation rules', () => {
    it('prevents duplicate results for the same player and match', async () => {
      try {
        await service.create({
          matchId: match.id,
          playerId: player.id,
          points: 270,
          maxPoints: 300,
          place: 3
        }, userParams)
        assert.fail('Should not allow duplicate results')
      } catch (error: any) {
        assert.ok(error instanceof BadRequest, 'Throws BadRequest for duplicate results')
        assert.ok(error.message.includes('already has a result'), 'Error message mentions already has a result')
      }
    })

    it('validates points cannot be negative', async () => {
      try {
        await service.patch(resultId, {
          points: -10
        }, userParams)
        assert.fail('Should not allow negative points')
      } catch (error: any) {
        assert.ok(error instanceof BadRequest, 'Throws BadRequest for negative points')
        assert.ok(error.message.includes('cannot be negative'), 'Error message mentions cannot be negative')
      }
    })

    it('validates max points must be greater than zero', async () => {
      try {
        await service.patch(resultId, {
          maxPoints: 0
        }, userParams)
        assert.fail('Should not allow max points of zero')
      } catch (error: any) {
        assert.ok(error instanceof BadRequest, 'Throws BadRequest for invalid max points')
        assert.ok(error.message.includes('greater than zero'), 'Error message mentions greater than zero')
      }
    })

    it('validates points cannot be greater than max points', async () => {
      try {
        await service.patch(resultId, {
          points: 350,
          maxPoints: 300
        }, userParams)
        assert.fail('Should not allow points greater than max points')
      } catch (error: any) {
        assert.ok(error instanceof BadRequest, 'Throws BadRequest for points > max points')
        assert.ok(error.message.includes('cannot be greater than max points'), 'Error message mentions cannot be greater than max points')
      }
    })

    it('validates place must be greater than zero', async () => {
      try {
        await service.patch(resultId, {
          place: 0
        }, userParams)
        assert.fail('Should not allow place of zero')
      } catch (error: any) {
        assert.ok(error instanceof BadRequest, 'Throws BadRequest for invalid place')
        assert.ok(error.message.includes('greater than zero'), 'Error message mentions greater than zero')
      }
    })
  })
})
