import { hooks as schemaHooks } from '@feathersjs/schema'

import type { Application } from '../../declarations'
import { FederationService, getOptions } from './federations.class'
import {
    federationDataResolver,
    federationDataValidator,
    federationExternalResolver,
    federationPatchResolver,
    federationPatchValidator,
    federationQueryResolver,
    federationQueryValidator,
    federationResolver
} from './federations.schema'

export const federationPath = 'federations'
export const federationMethods = ['find', 'get', 'create', 'patch', 'remove']

export * from './federations.class'
export * from './federations.schema'

export const federationHooks = {
    around: {
        all: [
            schemaHooks.resolveExternal(federationExternalResolver),
            schemaHooks.resolveResult(federationResolver)
        ]
    },
    before: {
        all: [
            schemaHooks.validateQuery(federationQueryValidator),
            schemaHooks.resolveQuery(federationQueryResolver)
        ],
        find: [],
        get: [],
        create: [
            schemaHooks.validateData(federationDataValidator),
            schemaHooks.resolveData(federationDataResolver)
        ],
        patch: [
            schemaHooks.validateData(federationPatchValidator),
            schemaHooks.resolveData(federationPatchResolver)
        ],
        remove: []
    },
    after: {
        all: []
    },
    error: {
        all: []
    }
}

export function federations(app: Application) {
    app.use(federationPath, new FederationService(getOptions(app)))
    // Register hooks
    app.service(federationPath).hooks(federationHooks)
}
