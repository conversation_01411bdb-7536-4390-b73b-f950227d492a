// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'

import type { ClientApplication } from '../../client'
import type {
  MatchRegistration,
  MatchRegistrationData,
  MatchRegistrationPatch,
  MatchRegistrationQuery,
  MatchRegistrationsService
} from './match-registrations.class'

export type { MatchRegistration, MatchRegistrationData, MatchRegistrationPatch, MatchRegistrationQuery }

export type MatchRegistrationsClientService = Pick<
  MatchRegistrationsService<Params<MatchRegistrationQuery>>,
  (typeof matchRegistrationsMethods)[number]
>

export const matchRegistrationsPath = 'match-registrations'

export const matchRegistrationsMethods: Array<keyof MatchRegistrationsService> = [
  'find',
  'get',
  'create',
  'patch',
  'remove'
]

export const matchRegistrationsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(matchRegistrationsPath, connection.service(matchRegistrationsPath), {
    methods: matchRegistrationsMethods
  })
}

// Add this service to the client service type index
declare module '../../client' {
  interface ServiceTypes {
    [matchRegistrationsPath]: MatchRegistrationsClientService
  }
}
