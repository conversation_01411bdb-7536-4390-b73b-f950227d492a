import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
    await knex.schema.alterTable('matches', table => {
        // Add new federationId column as nullable integer
        table.integer('federationId').nullable()
    })

    // Drop the old federation string column
    await knex.schema.alterTable('matches', table => {
        table.dropColumn('federation')
    })

    // Add foreign key constraint
    await knex.schema.alterTable('matches', table => {
        table.foreign('federationId').references('federations.id')
    })
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.alterTable('matches', table => {
        // Drop foreign key constraint
        table.dropForeign(['federationId'])
        // Drop federationId column
        table.dropColumn('federationId')
        // Re-add federation string column
        table.string('federation').nullable()
    })
}
