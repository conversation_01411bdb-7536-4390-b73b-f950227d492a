import { BadRequest } from '@feathersjs/errors'
import type { HookContext } from '@feathersjs/feathers'

import type { Application } from '../../../declarations'
import type { MatchesService } from '../matches.class'

/**
 * Validates that the organizer exists and is active
 */
export const validateOrganizer = () => {
  return async (context: HookContext<Application, MatchesService>) => {
    const { app, data, method } = context

    // Only run this hook for create and patch/update methods
    if (method !== 'create' && method !== 'patch' && method !== 'update') {
      return context
    }

    // Make sure data exists
    if (!data) {
      return context
    }

    // For create method, organizerId is required
    if (method === 'create' && (!('organizerId' in data) || data.organizerId === undefined)) {
      throw new BadRequest('Organizer is required when creating a match')
    }

    // If organizerId is not being set/changed, skip validation
    if (!('organizerId' in data) || data.organizerId === undefined) {
      return context
    }

    try {
      // Check if the organizer exists and is active
      const organizerId = data.organizerId
      const organizer = await app.service('organizers').get(organizerId)

      if (!organizer.isActive) {
        throw new BadRequest('The selected organizer is not active')
      }

      return context
    } catch (error: any) {
      // If the error is not a NotFound error, rethrow it
      if (error.name !== 'NotFound') {
        throw error
      }

      // If the organizer doesn't exist, throw a BadRequest error
      throw new BadRequest('The selected organizer does not exist')
    }
  }
}
