import { createSwaggerServiceOptions } from 'feathers-swagger'

// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import { authenticate } from '@feathersjs/authentication'
import { hooks as schemaHooks } from '@feathersjs/schema'

import type { Application } from '../../declarations'
import { populateUserTracking, skipIfDeletedByHook } from '../../hooks/user-tracking'
import { validateRegistration } from './hooks/validate-registration'
import { MatchRegistrationsService, getOptions } from './match-registrations.class'
import {
  matchRegistrationDataResolver,
  matchRegistrationDataSchema,
  matchRegistrationDataValidator,
  matchRegistrationExternalResolver,
  matchRegistrationPatchResolver,
  matchRegistrationPatchSchema,
  matchRegistrationPatchValidator,
  matchRegistrationQueryResolver,
  matchRegistrationQuerySchema,
  matchRegistrationQueryValidator,
  matchRegistrationResolver,
  matchRegistrationSchema
} from './match-registrations.schema'
import { matchRegistrationsMethods, matchRegistrationsPath } from './match-registrations.shared'

export { MatchRegistrationsService } from './match-registrations.class'

// A configure function that registers the service and its hooks via `app.configure`
export const matchRegistrations = (app: Application) => {
  // Register our service on the Feathers application
  app.use(matchRegistrationsPath, new MatchRegistrationsService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: matchRegistrationsMethods,
    // You can add additional custom events to be sent to clients here
    events: [],
    docs: createSwaggerServiceOptions({
      schemas: { matchRegistrationDataSchema, matchRegistrationQuerySchema, matchRegistrationSchema, matchRegistrationPatchSchema }, // Corrected: singular schema names
      docs: {
        description: 'Match registrations service',
        securities: ['all']
      }
    })
  })
  // Initialize hooks
  app.service(matchRegistrationsPath).hooks({
    around: {
      all: [
        authenticate('jwt'),
        schemaHooks.resolveExternal(matchRegistrationExternalResolver),
        schemaHooks.resolveResult(matchRegistrationResolver)
      ]
    },
    before: {
      all: [
        skipIfDeletedByHook(),
        schemaHooks.validateQuery(matchRegistrationQueryValidator),
        schemaHooks.resolveQuery(matchRegistrationQueryResolver)
      ],
      find: [],
      get: [],
      create: [
        validateRegistration(),
        schemaHooks.validateData(matchRegistrationDataValidator),
        schemaHooks.resolveData(matchRegistrationDataResolver),
        populateUserTracking()
      ],
      patch: [
        schemaHooks.validateData(matchRegistrationPatchValidator),
        schemaHooks.resolveData(matchRegistrationPatchResolver),
        populateUserTracking()
      ],
      remove: [populateUserTracking()]
    },
    after: {
      all: []
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    [matchRegistrationsPath]: MatchRegistrationsService
  }
}
