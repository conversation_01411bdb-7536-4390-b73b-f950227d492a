import { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
  // Drop the existing foreign key constraint
  await knex.schema.alterTable('players', (table) => {
    table.dropForeign(['userId'])
  })

  // Alter the column to be nullable
  await knex.schema.alterTable('players', (table) => {
    table.integer('userId').nullable().alter()
  })

  // Add the foreign key constraint back with SET NULL on delete
  await knex.schema.alterTable('players', (table) => {
    table.foreign('userId').references('id').inTable('users').onDelete('SET NULL')
  })
}

export async function down(knex: Knex): Promise<void> {
  // Drop the foreign key constraint
  await knex.schema.alterTable('players', (table) => {
    table.dropForeign(['userId'])
  })

  // First, we need to handle any NULL values by either deleting those records
  // or setting them to a valid user ID. For this migration, we'll delete players without users
  await knex('players').whereNull('userId').del()

  // Alter the column back to not nullable
  await knex.schema.alterTable('players', (table) => {
    table.integer('userId').notNullable().alter()
  })

  // Add the foreign key constraint back without CASCADE
  await knex.schema.alterTable('players', (table) => {
    table.foreign('userId').references('id').inTable('users')
  })
}
