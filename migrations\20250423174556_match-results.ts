// For more information about this file see https://dove.feathersjs.com/guides/cli/knexfile.html
import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
  // Drop the table if it exists
  if (await knex.schema.hasTable('match-results')) {
    await knex.schema.dropTable('match-results')
  }

  await knex.schema.createTable('match_results', table => {
    table.increments('id')
    table.integer('matchId').notNullable().references('id').inTable('matches')
    table.integer('playerId').notNullable().references('id').inTable('players')
    table.integer('points').notNullable()
    table.integer('maxPoints').notNullable()
    table.integer('place').nullable()
    table.timestamp('resultDate').defaultTo(knex.fn.now())
    table.timestamp('createdAt').defaultTo(knex.fn.now())
    table.timestamp('updatedAt').defaultTo(knex.fn.now())
    table.timestamp('deletedAt').nullable()
    table.integer('createdBy').nullable().references('id').inTable('users')
    table.integer('updatedBy').nullable().references('id').inTable('users')
    table.integer('deletedBy').nullable().references('id').inTable('users')

    // Add a unique constraint to ensure a player can only have one result per match
    table.unique(['matchId', 'playerId'])
  })
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('match_results')
}
