import { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
    await knex.schema.alterTable('organizers', (table) => {
        table.string('address').nullable()
        table.string('zipcode').nullable()
        table.string('city').nullable()
        table.string('country').nullable()
        table.string('phone').nullable()
        table.decimal('latitude', 10, 7).nullable()
        table.decimal('longitude', 10, 7).nullable()
        table.string('avatar').nullable()
    })
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.alterTable('organizers', (table) => {
        table.dropColumn('address')
        table.dropColumn('zipcode')
        table.dropColumn('city')
        table.dropColumn('country')
        table.dropColumn('phone')
        table.dropColumn('latitude')
        table.dropColumn('longitude')
        table.dropColumn('avatar')
    })
}
