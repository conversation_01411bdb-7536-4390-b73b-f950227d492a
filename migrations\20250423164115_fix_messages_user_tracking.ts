import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Convert createdAt from number to timestamp
  await knex.raw('ALTER TABLE messages ALTER COLUMN "createdAt" TYPE timestamp USING to_timestamp("createdAt")');

  // Add missing createdBy field to messages table
  await knex.schema.alterTable('messages', table => {
    // Add createdBy field
    table.integer('createdBy').nullable().references('id').inTable('users');
  });
}

export async function down(knex: Knex): Promise<void> {
  // Remove createdBy field from messages table
  await knex.schema.alterTable('messages', table => {
    table.dropColumn('createdBy');
  });

  // Convert createdAt back to number
  await knex.raw('ALTER TABLE messages ALTER COLUMN "createdAt" TYPE bigint USING extract(epoch from "createdAt")');
}
