// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'

import type { ClientApplication } from '../../client'
import type {
  Organizer,
  OrganizerData,
  OrganizerPatch,
  OrganizerQuery,
  OrganizersService
} from './organizers.class'

export type { Organizer, OrganizerData, OrganizerPatch, OrganizerQuery } // Changed Organizers to Organizer, etc.

export type OrganizersClientService = Pick<
  OrganizersService<Params<OrganizerQuery>>,
  (typeof organizersMethods)[number]
>

export const organizersPath = 'organizers'

export const organizersMethods: Array<keyof OrganizersService> = ['find', 'get', 'create', 'patch', 'remove']

export const organizersClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(organizersPath, connection.service(organizersPath), {
    methods: organizersMethods
  })
}

// Add this service to the client service type index
declare module '../../client' {
  interface ServiceTypes {
    [organizersPath]: OrganizersClientService
  }
}
