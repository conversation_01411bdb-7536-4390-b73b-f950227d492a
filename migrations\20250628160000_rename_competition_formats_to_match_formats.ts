import { Knex } from 'knex'

/**
 * Migration: Drop old competition_formats table and create new match_formats table
 * WARNING: This will remove all existing data in competition_formats.
 */
export async function up(knex: Knex): Promise<void> {
    await knex.schema.dropTableIfExists('competition_formats')

    await knex.schema.createTable('match_formats', (table) => {
        table.increments('id').primary()
        table.integer('legacyId')
        table.boolean('active').notNullable().defaultTo(true)
        table.string('name').notNullable()
        table.integer('numberOfShots')
        table.string('typesOfTargets')
        table.integer('targets')
        table.jsonb('scoring')
        table.string('matchType')
        table.integer('federationId').references('id').inTable('federations')
        table.timestamp('createdAt', { useTz: true }).defaultTo(knex.fn.now())
        table.timestamp('updatedAt', { useTz: true }).defaultTo(knex.fn.now())
        table.timestamp('deletedAt', { useTz: true })
        table.integer('createdBy')
        table.integer('updatedBy')
        table.integer('deletedBy')
    })
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.dropTableIfExists('match_formats')
    // Optionally, you could recreate the old competition_formats table here if needed
}
