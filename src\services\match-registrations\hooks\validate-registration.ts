import { BadRequest } from '@feathersjs/errors'

import type { HookContext } from '../../../declarations'
import type { MatchRegistrationData } from '../match-registrations.schema'

/**
 * Hook to validate match registration
 * - Checks if the match exists and is active
 * - Checks if registration is still open
 * - Checks if the match has reached maximum players
 * - Checks if the player meets match requirements (gender, etc.)
 */
export const validateRegistration = () => {
  return async (context: HookContext) => {
    const { app, data } = context
    const registrationData = data as MatchRegistrationData

    // Get the match
    try {
      const match = await app.service('matches').get(registrationData.matchId)

      // Check if match is active
      if (!match.isActive) {
        throw new BadRequest('This match is not active')
      }

      // Check if registration is still open
      if (match.registrationEnds && new Date(match.registrationEnds) < new Date()) {
        throw new BadRequest('Registration for this match has ended')
      }

      // Check if match is already at maximum capacity
      if (match.maxPlayersAmount) {
        const registrationsCount = await app.service('match-registrations').find({
          query: {
            matchId: match.id,
            status: { $ne: 'cancelled' },
            $limit: 0
          }
        })

        if (registrationsCount.total >= match.maxPlayersAmount) {
          throw new BadRequest('This match has reached its maximum number of players')
        }
      }

      // Get the player
      const player = await app.service('players').get(registrationData.playerId)

      // Check gender requirements if specified
      if ((match.forMen === true && match.forWomen === false && player.sex === 'female') ||
          (match.forWomen === true && match.forMen === false && player.sex === 'male')) {
        throw new BadRequest('Player does not meet gender requirements for this match')
      }

      // Validate style division if provided
      if (registrationData.styleDivision) {
        let matchEquipmentCategories: any[] = [];

        if (match.equipmentCategories) {
          if (typeof match.equipmentCategories === 'string') {
            matchEquipmentCategories = JSON.parse(match.equipmentCategories);
          } else {
            matchEquipmentCategories = match.equipmentCategories as any[];
          }
        }

        if (matchEquipmentCategories.length > 0 &&
            !matchEquipmentCategories.includes(registrationData.styleDivision)) {
          throw new BadRequest(`Invalid style division. Valid options are: ${matchEquipmentCategories.join(', ')}`)
        }
      }

      // Validate age division if provided
      if (registrationData.ageDivision) {
        let matchAgeCategories: any[] = [];

        if (match.ageCategories) {
          if (typeof match.ageCategories === 'string') {
            matchAgeCategories = JSON.parse(match.ageCategories);
          } else {
            matchAgeCategories = match.ageCategories as any[];
          }
        }

        if (matchAgeCategories.length > 0 &&
            !matchAgeCategories.includes(registrationData.ageDivision)) {
          throw new BadRequest(`Invalid age division. Valid options are: ${matchAgeCategories.join(', ')}`)
        }
      }

      // Validate gender division if provided
      if (registrationData.genderDivision) {
        const validGenderDivisions = ['male', 'female', 'mixed'];

        // Check if the provided gender division is valid
        if (!validGenderDivisions.includes(registrationData.genderDivision)) {
          throw new BadRequest(`Invalid gender division. Valid options are: ${validGenderDivisions.join(', ')}`)
        }

        // Check if the gender division is allowed for this match
        if ((registrationData.genderDivision === 'male' && !match.forMen) ||
            (registrationData.genderDivision === 'female' && !match.forWomen)) {
          throw new BadRequest(`Gender division '${registrationData.genderDivision}' is not allowed for this match`)
        }
      }

      // Check if player is already registered for this match
      const existingRegistration = await app.service('match-registrations').find({
        query: {
          matchId: registrationData.matchId,
          playerId: registrationData.playerId,
          status: { $ne: 'cancelled' },
          $limit: 0
        }
      })

      if (existingRegistration.total > 0) {
        throw new BadRequest('Player is already registered for this match')
      }

      // All validations passed
      return context
    } catch (error: any) {
      if (error.name === 'NotFound') {
        throw new BadRequest('Match not found')
      }
      throw error
    }
  }
}
