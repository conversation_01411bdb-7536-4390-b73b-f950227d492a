import { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
    await knex.schema.createTable('federations', (table) => {
        table.increments('id').primary()
        table.integer('legacyId').index()
        table.boolean('active').notNullable().defaultTo(false)
        table.string('name').notNullable()
        table.string('fullName')
        table.jsonb('ageDivisions')
        table.jsonb('styleDivisions')
        table.jsonb('disciplines')

        // User tracking and soft deletes
        table.timestamp('createdAt', { useTz: true }).defaultTo(knex.fn.now())
        table.timestamp('updatedAt', { useTz: true }).defaultTo(knex.fn.now())
        table.timestamp('deletedAt', { useTz: true }).nullable()
        table.string('createdBy').nullable()
        table.string('updatedBy').nullable()
        table.string('deletedBy').nullable()
    })
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.dropTable('federations')
}
