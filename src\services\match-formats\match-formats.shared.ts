// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'

import type { ClientApplication } from '../../client'
import type { MatchFormat, MatchFormatData, MatchFormatPatch, MatchFormatQuery, MatchFormatService } from './match-formats.class'

export type { MatchFormat, MatchFormatData, MatchFormatPatch, MatchFormatQuery }

export type MatchFormatClientService = Pick<MatchFormatService<Params<MatchFormatQuery>>, (typeof matchFormatsMethods)[number]>

export const matchFormatsPath = 'match-formats'

export const matchFormatsMethods: Array<keyof MatchFormatService> = ['find', 'get', 'create', 'patch', 'remove']

export const matchFormatsClient = (client: ClientApplication) => {
    const connection = client.get('connection')

    client.use(matchFormatsPath, connection.service(matchFormatsPath), {
        methods: matchFormatsMethods
    })
}

// Add this service to the client service type index
declare module '../../client' {
    interface ServiceTypes {
        [matchFormatsPath]: MatchFormatClientService
    }
}
