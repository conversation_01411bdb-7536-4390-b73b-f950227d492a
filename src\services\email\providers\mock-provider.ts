// src/services/email/providers/mock-provider.ts
import { appendFileSync, existsSync, mkdirSync, writeFileSync } from 'fs';
import { join } from 'path';

import { EmailOptions, EmailProvider } from './email-provider.interface';

export class MockEmailProvider implements EmailProvider {
  private sentEmails: EmailOptions[] = [];
  private logFilePath: string;
  private logDir: string;

  constructor() {
    this.logDir = join(process.cwd(), 'logs');
    this.logFilePath = join(this.logDir, 'mock-emails.txt');
    this.ensureLogDirectoryExists();
  }

  private ensureLogDirectoryExists(): void {
    if (!existsSync(this.logDir)) {
      mkdirSync(this.logDir, { recursive: true });
    }
  }

  async send(options: EmailOptions): Promise<any> {
    // Store in memory
    this.sentEmails.push(options);

    // Create log entry
    const timestamp = new Date().toISOString();
    const logEntry = `
=== EMAIL SENT ===
Timestamp: ${timestamp}
To: ${Array.isArray(options.to) ? options.to.join(', ') : options.to}
From: ${options.from || 'N/A'}
Subject: ${options.subject}
Text: ${options.text || 'N/A'}
HTML: ${options.html || 'N/A'}
Template: ${options.template || 'N/A'}
Template Data: ${options.templateData ? JSON.stringify(options.templateData, null, 2) : 'N/A'}
==================

`;

    // Append to file
    try {
      appendFileSync(this.logFilePath, logEntry, 'utf-8');
    } catch (error) {
      console.error('Failed to write email to log file:', error);
    }

    // Log to console for development
    console.log('📧 Mock Email Sent:', {
      to: options.to,
      subject: options.subject,
      timestamp
    });

    return { messageId: `mock-${Date.now()}-${Math.random()}` };
  }

  // Test helper methods (same as TestEmailProvider)
  getSentEmails(): EmailOptions[] {
    return [...this.sentEmails];
  }

  clearSentEmails(): void {
    this.sentEmails = [];
  }

  getLastSentEmail(): EmailOptions | undefined {
    return this.sentEmails[this.sentEmails.length - 1];
  }

  getSentEmailCount(): number {
    return this.sentEmails.length;
  }

  // Mock-specific methods
  getLogFilePath(): string {
    return this.logFilePath;
  }

  clearLogFile(): void {
    try {
      writeFileSync(this.logFilePath, '', 'utf-8');
    } catch (error) {
      console.error('Failed to clear email log file:', error);
    }
  }
}
