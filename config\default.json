{"host": "localhost", "port": 3030, "public": "./public/", "origins": ["http://localhost:3030"], "paginate": {"default": 10, "max": 100}, "postgresql": {"client": "pg", "connection": "postgres://postgres:cruARCHERY7*@localhost:5432/postgres"}, "email": {"provider": "mock", "defaultFrom": "<EMAIL>", "smtp": {"host": "smtp.gmail.com", "port": 587, "secure": false, "auth": {"user": "<EMAIL>", "pass": "your-app-password"}}}, "authentication": {"entity": "user", "service": "users", "secret": "olOMBt69HraJg8pb0Gu6hLuRq1n6jUPf", "authStrategies": ["jwt", "local", "github"], "jwtOptions": {"header": {"typ": "access"}, "audience": "https://yourdomain.com", "algorithm": "HS256", "expiresIn": "1d"}, "local": {"usernameField": "email", "passwordField": "password"}, "oauth": {"google": {"key": "924293608054-qo50t689j8j41ve06pr39uumcephblmf.apps.googleusercontent.com", "secret": "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"}, "facebook": {"key": "<Client ID>", "secret": "<Client secret>"}, "github": {"key": "Ov23liBY0hxOKtglII4x", "secret": "9fc4dab65ac333c357619110c8f17cf050048d5d", "scope": ["user:email"]}}}}