// src/services/email/providers/test-provider.ts
import { EmailOptions, EmailProvider } from './email-provider.interface';

export class TestEmailProvider implements EmailProvider {
  public sentEmails: EmailOptions[] = [];

  async send(options: EmailOptions): Promise<any> {
    this.sentEmails.push(options);
    console.log('Test email sent:', options);
    return { messageId: `test-${Date.now()}` };
  }

  getSentEmails(): EmailOptions[] {
    return this.sentEmails;
  }

  clearSentEmails(): void {
    this.sentEmails = [];
  }
}
