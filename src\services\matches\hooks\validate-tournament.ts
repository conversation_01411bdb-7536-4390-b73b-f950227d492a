import { BadRequest } from '@feathersjs/errors'
import type { HookContext } from '@feathersjs/feathers'

import type { Application } from '../../../declarations'
import type { Tournament } from '../../tournaments/tournaments.schema'
import type { MatchesService } from '../matches.class'

/**
 * Validates that the tournament exists and is active if provided
 */
export const validateTournament = () => {
  return async (context: HookContext<Application, MatchesService>) => {
    const { app, data, method } = context

    // Only run this hook for create and patch/update methods
    if (method !== 'create' && method !== 'patch' && method !== 'update') {
      return context
    }

    // Make sure data exists
    if (!data) {
      return context
    }

    // If tournamentId is not being set/changed, skip validation
    if (!('tournamentId' in data) || data.tournamentId === undefined || data.tournamentId === null) {
      return context
    }

    try {
      // Check if the tournament exists and is active
      const tournamentId = data.tournamentId
      const tournament: Tournament = await app.service('tournaments').get(tournamentId)

      if (!tournament.isActive) {
        throw new BadRequest('The selected tournament is not active')
      }

      return context
    } catch (error: any) {
      // If the error is not a NotFound error, rethrow it
      if (error.name !== 'NotFound') {
        throw error
      }

      // If the tournament doesn't exist, throw a BadRequest error
      throw new BadRequest('The selected tournament does not exist')
    }
  }
}
