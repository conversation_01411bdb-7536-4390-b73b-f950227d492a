import { createSwaggerServiceOptions } from 'feathers-swagger'

// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import { authenticate } from '@feathersjs/authentication'
import { hooks as schemaHooks } from '@feathersjs/schema'

import type { Application } from '../../declarations'
import { populateUserTracking, skipIfDeletedByHook } from '../../hooks/user-tracking'
import { OrganizersService, getOptions } from './organizers.class'
import {
  organizerDataResolver,
  organizerDataSchema,
  organizerDataValidator,
  organizerExternalResolver,
  organizerPatchResolver,
  organizerPatchSchema,
  organizerPatchValidator,
  organizerQueryResolver,
  organizerQuerySchema,
  organizerQueryValidator,
  organizerResolver,
  organizerSchema
} from './organizers.schema'
import { organizersMethods, organizersPath } from './organizers.shared'

export * from './organizers.class'
export * from './organizers.schema'

// A configure function that registers the service and its hooks via `app.configure`
export const organizers = (app: Application) => {
  // Register our service on the Feathers application
  app.use(organizersPath, new OrganizersService(getOptions(app)), {
    methods: organizersMethods,
    events: [],
    docs: createSwaggerServiceOptions({
      schemas: { organizerDataSchema, organizerQuerySchema, organizerSchema, organizerPatchSchema },
      docs: {
        description: 'Organizers service',
        securities: ['all']
      }
    })
  })
  // Initialize hooks
  app.service(organizersPath).hooks({
    around: {
      all: [
        authenticate('jwt'),
        schemaHooks.resolveExternal(organizerExternalResolver),
        schemaHooks.resolveResult(organizerResolver)
      ]
    },
    before: {
      all: [
        skipIfDeletedByHook(),
        schemaHooks.validateQuery(organizerQueryValidator),
        schemaHooks.resolveQuery(organizerQueryResolver)
      ],
      find: [],
      get: [],
      create: [
        schemaHooks.validateData(organizerDataValidator),
        schemaHooks.resolveData(organizerDataResolver),
        populateUserTracking()
      ],
      patch: [
        schemaHooks.validateData(organizerPatchValidator),
        schemaHooks.resolveData(organizerPatchResolver),
        populateUserTracking()
      ],
      remove: [populateUserTracking()]
    },
    after: {
      all: []
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    [organizersPath]: OrganizersService // Ensured OrganizersService is used
  }
}
