import type { Id, NullableId, Params } from '@feathersjs/feathers'
import { KnexService } from '@feathersjs/knex'

export class FederationService extends KnexService {
    // All custom logic or overrides can be placed here
    // For now, using the base implementation per project pattern
}

// Helper to return options object for service registration
export const getOptions = (app: any) => {
    return {
        Model: app.get('postgresqlClient'),
        name: 'federations'
    }
}

// Types for federation service schema
export type { Federation, FederationData, FederationPatch, FederationQuery } from './federations.schema'
