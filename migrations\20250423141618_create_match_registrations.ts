import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable('match_registrations', table => {
    table.increments('id');
    table.integer('matchId').references('id').inTable('matches').notNullable();
    table.integer('playerId').references('id').inTable('players').notNullable();
    table.timestamp('registrationDate').defaultTo(knex.fn.now());
    table.enum('status', ['pending', 'confirmed', 'cancelled']).defaultTo('pending');
    table.string('equipmentCategory').comment('Equipment category the player is registering for');
    table.string('ageCategory').comment('Age category the player is registering for');
    table.jsonb('registrationDetails').comment('Additional registration details');
    table.timestamp('createdAt').defaultTo(knex.fn.now());
    table.timestamp('updatedAt').defaultTo(knex.fn.now());

    // Add unique constraint to prevent duplicate registrations
    table.unique(['matchId', 'playerId']);

    // Add indexes for faster lookups
    table.index(['matchId']);
    table.index(['playerId']);
    table.index(['status']);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('match_registrations');
}
