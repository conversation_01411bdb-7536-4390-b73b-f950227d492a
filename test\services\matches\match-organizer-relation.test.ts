import assert from 'assert'

import { app } from '../../../src/app'
import type { Match } from '../../../src/services/matches/matches.schema' // Corrected import

describe('Match-Organizer relationship', () => {
  const matchesService = app.service('matches')
  const organizersService = app.service('organizers')
  const usersService = app.service('users')

  let user: any
  let organizer: any
  let match: Match // Corrected type
  let userParams: any

  before(async () => {
    // Create a test user
    user = await usersService.create({
      email: `test-match-organizer-${Date.now()}@example.com`,
      password: 'supersecret'
    })

    // Create authentication params
    const { accessToken } = await app.service('authentication').create({
      strategy: 'local',
      email: user.email,
      password: 'supersecret'
    })

    userParams = {
      provider: 'rest',
      authentication: {
        strategy: 'jwt',
        accessToken
      },
      user
    }

    // Create a test organizer
    organizer = await organizersService.create({
      userId: user.id,
      name: 'Test Organizer for Match Relation',
      bankAccount: 'DE123456789',
      taxId: 'TAX123456',
      contact<PERSON>erson: '<PERSON>',
      about: 'This is a test organizer for testing match relationships',
      isActive: true
    }, userParams)
  })

  after(async () => {
    try {
      // Clean up - need to remove in the correct order due to foreign key constraints
      if (match) {
        // First remove the organizer reference
        await matchesService.patch(match.id, { organizerId: undefined }, userParams)
        // Then remove the match
        await matchesService.remove(match.id, userParams)
      }

      if (organizer) {
        await organizersService.remove(organizer.id, userParams)
      }

      if (user) {
        await usersService.remove(user.id)
      }
    } catch (error) {
      console.error('Error cleaning up test data:', error)
    }
  })

  it('creates a match with an organizer reference', async () => {
    // Create a match with a reference to the organizer
    match = await matchesService.create({
      name: 'Test Match with Organizer',
      isActive: true,
      description: 'A test match with an organizer relationship',
      equipmentCategories: JSON.stringify(['recurve', 'compound']),
      ageCategories: JSON.stringify(['senior', 'junior']),
      forMen: true,
      forWomen: true,
      maxPlayersAmount: 10,
      organizerId: organizer.id
    }, userParams)

    assert.ok(match, 'Created a match')
    assert.strictEqual(match.organizerId as number, organizer.id, 'Match has the correct organizer ID')
  })

  it('loads the organizer when getting a match', async () => {
    // Get the match with the virtual organizer field populated
    const retrievedMatch: Match = await matchesService.get(match.id, userParams)

    assert.ok(retrievedMatch, 'Retrieved the match')
    assert.ok(retrievedMatch.organizer, 'Match has the organizer field populated')
    assert.strictEqual(retrievedMatch.organizer.id, organizer.id, 'Organizer ID matches')
    assert.strictEqual(retrievedMatch.organizer.name, 'Test Organizer for Match Relation', 'Organizer name matches')
  })

  it('finds matches by organizer ID', async () => {
    // Find matches with the specific organizer ID
    const matches = await matchesService.find({
      query: {
        organizerId: organizer.id
      },
      ...userParams
    })

    assert.ok(matches.data, 'Returns data')
    assert.ok(matches.total >= 1, 'Returns at least one match')
    assert.strictEqual(matches.data[0].organizerId as number, organizer.id, 'Match has the correct organizer ID')
  })

  it('updates a match to change the organizer', async () => {
    // Create another organizer
    const anotherOrganizer = await organizersService.create({
      userId: user.id,
      name: 'Another Test Organizer',
      isActive: true
    }, userParams)

    // Update the match to use the new organizer
    const updatedMatch: Match = await matchesService.patch(match.id, {
      organizerId: anotherOrganizer.id
    }, userParams) as Match // Added type assertion

    assert.ok(updatedMatch, 'Updated the match')
    assert.strictEqual(updatedMatch.organizerId as number, anotherOrganizer.id, 'Match now has the new organizer ID')

    // Update the match to use the original organizer again
    await matchesService.patch(match.id, {
      organizerId: organizer.id
    }, userParams)

    // Verify the match has the original organizer ID again
    const finalMatch: Match = await matchesService.get(match.id, userParams)
    assert.strictEqual(finalMatch.organizerId as number, organizer.id, 'Match has the original organizer ID again')

    // Clean up the additional organizer
    await organizersService.remove(anotherOrganizer.id, userParams)
  })
})
