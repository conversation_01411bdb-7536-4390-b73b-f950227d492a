// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { KnexService } from '@feathersjs/knex'
import type { KnexAdapterOptions, KnexAdapterParams } from '@feathersjs/knex'

import type { Application } from '../../declarations'
import type { MatchFormat, MatchFormatData, MatchFormatPatch, MatchFormatQuery } from './match-formats.schema'

export type { MatchFormat, MatchFormatData, MatchFormatPatch, MatchFormatQuery } from './match-formats.schema'

export interface MatchFormatParams extends KnexAdapterParams<MatchFormatQuery> { }

// By default calls the standard Knex adapter service methods but can be customized with your own functionality.
export class MatchFormatService<ServiceParams extends Params = MatchFormatParams> extends KnexService<
    MatchFormat,
    MatchFormatData,
    MatchFormatParams,
    MatchFormatPatch
> { }

export const getOptions = (app: Application): KnexAdapterOptions => {
    return {
        paginate: app.get('paginate'),
        Model: app.get('postgresqlClient'),
        name: 'match_formats'
    }
}
