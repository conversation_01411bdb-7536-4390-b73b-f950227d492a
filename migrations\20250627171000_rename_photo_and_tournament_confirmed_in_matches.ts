import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
    await knex.schema.alterTable('matches', table => {
        // Rename photo column to coverImageUrl
        table.renameColumn('photo', 'coverImageUrl')
        // Drop the old tournamentConfirmed boolean column
        table.dropColumn('tournamentConfirmed')
        // Add new tournamentConfirmedAt timestamp column
        table.timestamp('tournamentConfirmedAt').nullable()
    })
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.alterTable('matches', table => {
        // Rename back to original names
        table.renameColumn('coverImageUrl', 'photo')
        // Drop the new tournamentConfirmedAt timestamp column
        table.dropColumn('tournamentConfirmedAt')
        // Add back the original tournamentConfirmed boolean column
        table.boolean('tournamentConfirmed').nullable()
    })
}
