import * as fs from 'fs';
import * as path from 'path';

import * as handlebars from 'handlebars';

// src/hooks/send-templated-email.ts
import { HookContext } from '@feathersjs/feathers';

interface EmailTemplateOptions {
  template: string;
  to: string | ((context: HookContext) => string);
  subject: string | ((context: HookContext) => string);
  data?: any | ((context: HookContext) => any);
}

export const sendTemplatedEmail = (options: EmailTemplateOptions) => {
  return async (context: HookContext) => {
    const { app, result } = context;
    const emailService = app.service('/_mailer');

    try {
      // Load and compile template
      const templatePath = path.join(app.get('src'), 'email-templates', `${options.template}.hbs`);
      const templateSource = fs.readFileSync(templatePath, 'utf8');
      const template = handlebars.compile(templateSource);

      // Resolve dynamic values
      const to = typeof options.to === 'function' ? options.to(context) : options.to;
      const subject = typeof options.subject === 'function' ? options.subject(context) : options.subject;
      const data = typeof options.data === 'function' ? options.data(context) : options.data;

      // Render template
      const html = template({ ...data, result });

      await emailService.create({
        to,
        subject,
        html
      });
    } catch (error) {
      console.error('Failed to send templated email:', error);
    }

    return context;
  };
};
