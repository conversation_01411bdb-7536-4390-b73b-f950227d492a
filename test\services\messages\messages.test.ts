// For more information about this file see https://dove.feathersjs.com/guides/cli/service.test.html
import assert from 'assert'

import { app } from '../../../src/app'

describe('messages service', () => {
  const service = app.service('messages')
  const usersService = app.service('users')
  const authService = app.service('authentication')

  let userId: number
  let messageId: number
  let accessToken: string
  let userParams: any

  // Create a test user before running message tests
  before(async () => {
    const testUser = await usersService.create({
      email: `test-messages-${Date.now()}@example.com`,
      password: 'supersecret'
    })

    userId = testUser.id

    // Authenticate the user
    const authResult = await authService.create({
      strategy: 'local',
      email: testUser.email,
      password: 'supersecret'
    })

    accessToken = authResult.accessToken

    // Create params with authentication
    userParams = {
      provider: 'rest',
      authentication: {
        strategy: 'jwt',
        accessToken
      },
      user: testUser
    }
  })

  // Clean up test user after tests
  after(async () => {
    try {
      await usersService.remove(userId)
    } catch (error) {
      console.error('Error cleaning up test user:', error)
    }
  })

  it('registered the service', () => {
    assert.ok(service, 'Registered the service')
  })

  it('requires authentication', async () => {
    try {
      await service.find()
      assert.fail('Should not allow unauthenticated access')
    } catch (error: any) {
      assert.ok(error, 'Returns an error for unauthenticated access')
    }
  })

  it('creates a message with authenticated user', async () => {
    const messageData = {
      text: 'Test message'
    }

    const message = await service.create(messageData, userParams)

    assert.ok(message, 'Created a message')
    assert.ok(message.id, 'Message has an id')
    assert.equal(message.text, messageData.text, 'Sets the text')
    assert.equal(message.userId, userId, 'Associates with the authenticated user')
    assert.ok(message.createdAt, 'Sets createdAt timestamp')

    // Save message ID for later tests
    messageId = message.id
  })

  it('gets a message', async () => {
    const message = await service.get(messageId, userParams)

    assert.ok(message, 'Got the message')
    assert.equal(message.id, messageId, 'Got the correct message')
    assert.equal(message.text, 'Test message', 'Retrieved text matches')
    assert.equal(message.userId, userId, 'User association is maintained')
  })

  it('finds messages with pagination', async () => {
    const result = await service.find({
      ...userParams,
      query: {
        $limit: 10
      }
    })

    assert.ok(result.data, 'Returns data array')
    assert.ok(result.total >= 1, 'Returns at least one message')
    assert.ok(result.limit === 10, 'Returns specified limit')
  })

  it('updates a message', async () => {
    const updatedData = {
      text: 'Updated test message'
    }

    const updated = await service.patch(messageId, updatedData, userParams)

    assert.equal(updated.text, updatedData.text, 'Updated the text')
    assert.equal(updated.id, messageId, 'ID remained the same')
    assert.equal(updated.userId, userId, 'User association remained the same')
  })

  it('removes a message', async () => {
    const removed = await service.remove(messageId, userParams)

    assert.ok(removed, 'Removed the message')
    assert.equal(removed.id, messageId, 'Removed the correct message')

    try {
      await service.get(messageId, userParams)
      assert.fail('Should have thrown an error for deleted message')
    } catch (error) {
      assert.ok(error, 'Error thrown when getting deleted message')
    }
  })
})
