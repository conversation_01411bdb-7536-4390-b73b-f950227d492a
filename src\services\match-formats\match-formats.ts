import { createSwaggerServiceOptions } from 'feathers-swagger'

// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import { authenticate } from '@feathersjs/authentication'
import { hooks as schemaHooks } from '@feathersjs/schema'

import type { Application } from '../../declarations'
import { MatchFormatService, getOptions } from './match-formats.class'
import {
  matchFormatDataResolver,
  matchFormatDataSchema,
  matchFormatDataValidator,
  matchFormatExternalResolver,
  matchFormatPatchResolver,
  matchFormatPatchSchema,
  matchFormatPatchValidator,
  matchFormatQueryResolver,
  matchFormatQuerySchema,
  matchFormatQueryValidator,
  matchFormatResolver,
  matchFormatSchema
} from './match-formats.schema'
import { matchFormatsMethods, matchFormatsPath } from './match-formats.shared'

export * from './match-formats.class'
export * from './match-formats.schema'

// A configure function that registers the service and its hooks via `app.configure`
export const matchFormats = (app: Application) => {
  // Register our service on the Feathers application
  app.use(matchFormatsPath, new MatchFormatService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: matchFormatsMethods,
    // You can add additional custom events to be sent to clients here
    events: [],
    docs: createSwaggerServiceOptions({
      schemas: { matchFormatDataSchema, matchFormatQuerySchema, matchFormatSchema, matchFormatPatchSchema },
      docs: {
        description: 'Match formats service',
        securities: ['all']
      }
    })
  })
  // Initialize hooks
  app.service(matchFormatsPath).hooks({
    around: {
      all: [
        authenticate('jwt'),
        schemaHooks.resolveExternal(matchFormatExternalResolver),
        schemaHooks.resolveResult(matchFormatResolver)
      ]
    },
    before: {
      all: [
        schemaHooks.validateQuery(matchFormatQueryValidator),
        schemaHooks.resolveQuery(matchFormatQueryResolver)
      ],
      find: [],
      get: [],
      create: [
        schemaHooks.validateData(matchFormatDataValidator),
        schemaHooks.resolveData(matchFormatDataResolver)
      ],
      patch: [
        schemaHooks.validateData(matchFormatPatchValidator),
        schemaHooks.resolveData(matchFormatPatchResolver)
      ],
      remove: []
    },
    after: {
      all: []
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    [matchFormatsPath]: MatchFormatService
  }
}
