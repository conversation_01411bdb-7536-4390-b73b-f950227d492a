// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { KnexService } from '@feathersjs/knex'
import type { KnexAdapterOptions, KnexAdapterParams } from '@feathersjs/knex'

import type { Application } from '../../declarations'
import type { Organizer, OrganizerData, OrganizerPatch, OrganizerQuery } from './organizers.schema'

export type { Organizer, OrganizerData, OrganizerPatch, OrganizerQuery }

export interface OrganizerParams extends KnexAdapterParams<OrganizerQuery> {}

// By default calls the standard Knex adapter service methods but can be customized with your own functionality.
export class OrganizersService<ServiceParams extends Params = OrganizerParams> extends KnexService<
  Organizer,
  OrganizerData,
  OrganizerParams,
  OrganizerPatch
> {}

export const getOptions = (app: Application): KnexAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('postgresqlClient'),
    name: 'organizers'
  }
}
