import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Add legacyId column to matches table
  return knex.schema.table('matches', table => {
    table.integer('legacyId').nullable().comment('Legacy ID from the original database');
    
    // Add an index for faster lookups by legacyId
    table.index(['legacyId'], 'idx_matches_legacy_id');
  });
}

export async function down(knex: Knex): Promise<void> {
  // Remove legacyId column in case of rollback
  return knex.schema.table('matches', table => {
    table.dropIndex(['legacyId'], 'idx_matches_legacy_id');
    table.dropColumn('legacyId');
  });
}

