// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { KnexService } from '@feathersjs/knex'
import type { KnexAdapterOptions, KnexAdapterParams } from '@feathersjs/knex'

import type { Application } from '../../declarations'
import type { Player, PlayerData, PlayerPatch, PlayerQuery } from './players.schema'

export type { Player, PlayerData, PlayerPatch, PlayerQuery }

export interface PlayerParams extends KnexAdapterParams<PlayerQuery> {}

// By default calls the standard Knex adapter service methods but can be customized with your own functionality.
export class PlayersService<ServiceParams extends Params = PlayerParams> extends KnexService<
  Player,
  PlayerData,
  PlayerParams,
  PlayerPatch
> {}

export const getOptions = (app: Application): KnexAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('postgresqlClient'),
    name: 'players'
  }
}
