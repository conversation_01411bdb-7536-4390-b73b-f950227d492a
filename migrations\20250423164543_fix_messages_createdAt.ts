import type { K<PERSON> } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Drop the messages table and recreate it with the correct schema
  await knex.schema.dropTable('messages');

  // Recreate the messages table with the correct schema
  await knex.schema.createTable('messages', table => {
    table.increments('id');
    table.string('text').notNullable();
    table.timestamp('createdAt').defaultTo(knex.fn.now());
    table.timestamp('updatedAt').defaultTo(knex.fn.now());
    table.timestamp('deletedAt').nullable();
    table.integer('userId').notNullable().references('id').inTable('users');
    table.integer('createdBy').nullable().references('id').inTable('users');
    table.integer('updatedBy').nullable().references('id').inTable('users');
    table.integer('deletedBy').nullable().references('id').inTable('users');
  });
}

export async function down(knex: Knex): Promise<void> {
  // Drop the messages table
  await knex.schema.dropTable('messages');

  // Recreate the original messages table
  await knex.schema.createTable('messages', table => {
    table.increments('id');
    table.string('text').notNullable();
    table.bigInteger('createdAt').notNullable();
    table.integer('userId').notNullable().references('id').inTable('users');
  });
}
