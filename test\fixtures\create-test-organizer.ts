import { app } from '../../src/app'

/**
 * Creates a test organizer for use in tests
 * @param userId The user ID to associate with the organizer
 * @param params Authentication params
 * @returns The created organizer
 */
export async function createTestOrganizer(userId: number, params: any) {
  const organizersService = app.service('organizers')
  
  return await organizersService.create({
    userId,
    name: 'Test Organizer',
    bankAccount: 'DE123456789',
    taxId: 'TAX123456',
    contactPerson: '<PERSON>',
    about: 'This is a test organizer for testing',
    isActive: true
  }, params)
}
