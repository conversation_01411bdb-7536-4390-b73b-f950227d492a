// test/services/email/mock-provider.test.ts
import assert from 'assert';
import { existsSync, readFileSync, unlinkSync } from 'fs';

import { MockEmailProvider } from '../../../src/services/email/providers/mock-provider';

describe('MockEmailProvider', () => {
  let mockProvider: MockEmailProvider;

  beforeEach(() => {
    mockProvider = new MockEmailProvider();
    mockProvider.clearSentEmails();
    mockProvider.clearLogFile();
  });

  afterEach(() => {
    // Clean up log file after tests
    try {
      const logPath = mockProvider.getLogFilePath();
      if (existsSync(logPath)) {
        unlinkSync(logPath);
      }
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  it('should send email and store in memory', async () => {
    const emailData = {
      to: '<EMAIL>',
      subject: 'Test Subject',
      text: 'Test content'
    };

    const result = await mockProvider.send(emailData);

    const sentEmails = mockProvider.getSentEmails();
    assert.strictEqual(sentEmails.length, 1);
    assert.strictEqual(sentEmails[0].to, '<EMAIL>');
    assert.strictEqual(sentEmails[0].subject, 'Test Subject');
    assert.ok(result.messageId);
    assert.ok(result.messageId.startsWith('mock-'));
  });

  it('should save email to log file', async () => {
    const emailData = {
      to: '<EMAIL>',
      subject: 'File Test',
      text: 'This should be saved to file'
    };

    await mockProvider.send(emailData);

    const logPath = mockProvider.getLogFilePath();
    assert.ok(existsSync(logPath));

    const logContent = readFileSync(logPath, 'utf-8');
    assert.ok(logContent.includes('<EMAIL>'));
    assert.ok(logContent.includes('File Test'));
    assert.ok(logContent.includes('This should be saved to file'));
    assert.ok(logContent.includes('=== EMAIL SENT ==='));
  });

  it('should handle multiple emails', async () => {
    const emails = [
      { to: '<EMAIL>', subject: 'Email 1', text: 'Content 1' },
      { to: '<EMAIL>', subject: 'Email 2', text: 'Content 2' }
    ];

    for (const email of emails) {
      await mockProvider.send(email);
    }

    assert.strictEqual(mockProvider.getSentEmailCount(), 2);
    
    const logContent = readFileSync(mockProvider.getLogFilePath(), 'utf-8');
    assert.ok(logContent.includes('<EMAIL>'));
    assert.ok(logContent.includes('<EMAIL>'));
  });

  it('should clear sent emails', async () => {
    await mockProvider.send({
      to: '<EMAIL>',
      subject: 'Clear Test',
      text: 'This will be cleared'
    });

    assert.strictEqual(mockProvider.getSentEmailCount(), 1);
    
    mockProvider.clearSentEmails();
    
    assert.strictEqual(mockProvider.getSentEmailCount(), 0);
  });

  it('should get last sent email', async () => {
    const email1 = { to: '<EMAIL>', subject: 'First', text: 'First email' };
    const email2 = { to: '<EMAIL>', subject: 'Second', text: 'Second email' };

    await mockProvider.send(email1);
    await mockProvider.send(email2);

    const lastEmail = mockProvider.getLastSentEmail();
    assert.ok(lastEmail);
    assert.strictEqual(lastEmail.to, '<EMAIL>');
    assert.strictEqual(lastEmail.subject, 'Second');
  });

  it('should handle array of recipients', async () => {
    const emailData = {
      to: ['<EMAIL>', '<EMAIL>'],
      subject: 'Multiple Recipients',
      text: 'This goes to multiple people'
    };

    await mockProvider.send(emailData);

    const logContent = readFileSync(mockProvider.getLogFilePath(), 'utf-8');
    assert.ok(logContent.includes('<EMAIL>, <EMAIL>'));
  });

  it('should handle template data', async () => {
    const emailData = {
      to: '<EMAIL>',
      subject: 'Template Test',
      template: 'welcome',
      templateData: { name: 'John', company: 'ACME Corp' }
    };

    await mockProvider.send(emailData);

    const logContent = readFileSync(mockProvider.getLogFilePath(), 'utf-8');
    assert.ok(logContent.includes('welcome'));
    assert.ok(logContent.includes('John'));
    assert.ok(logContent.includes('ACME Corp'));
  });
});
