// For more information about this file see https://dove.feathersjs.com/guides/cli/knexfile.html
import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable('organizers', table => {
    table.increments('id')
    table.integer('userId').references('id').inTable('users').notNullable()
    table.string('name').notNullable()
    table.string('bankAccount')
    table.string('taxId')
    table.string('contactPerson')
    table.text('about')
    table.boolean('isActive').defaultTo(true)
    table.timestamp('createdAt').defaultTo(knex.fn.now())
    table.timestamp('updatedAt').defaultTo(knex.fn.now())
    table.timestamp('deletedAt').nullable()
    table.integer('createdBy').nullable().references('id').inTable('users')
    table.integer('updatedBy').nullable().references('id').inTable('users')
    table.integer('deletedBy').nullable().references('id').inTable('users')
  })
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('organizers')
}
