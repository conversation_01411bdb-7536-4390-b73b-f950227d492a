import { createSwaggerServiceOptions } from 'feathers-swagger'

// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import { authenticate } from '@feathersjs/authentication'
import { hooks as schemaHooks } from '@feathersjs/schema'

import type { Application } from '../../declarations'
import { populateUserTracking, skipIfDeletedByHook } from '../../hooks/user-tracking'
import { validateMatchResult } from './hooks/validate-match-result'
import { MatchResultsService, getOptions } from './match-results.class'
import {
  matchResultDataResolver,
  matchResultDataSchema,
  matchResultDataValidator,
  matchResultExternalResolver,
  matchResultPatchResolver,
  matchResultPatchSchema,
  matchResultPatchValidator,
  matchResultQueryResolver,
  matchResultQuerySchema,
  matchResultQueryValidator,
  matchResultResolver,
  matchResultSchema
} from './match-results.schema'
import { matchResultsMethods, matchResultsPath } from './match-results.shared'

export * from './match-results.class'
export * from './match-results.schema'

// A configure function that registers the service and its hooks via `app.configure`
export const matchResults = (app: Application) => {
  // Register our service on the Feathers application
  app.use(matchResultsPath, new MatchResultsService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: matchResultsMethods,
    // You can add additional custom events to be sent to clients here
    events: [],
    docs: createSwaggerServiceOptions({
      schemas: { matchResultDataSchema, matchResultQuerySchema, matchResultSchema, matchResultPatchSchema },
      docs: {
        description: 'Match results service',
        securities: ['all']
      }
    })
  })
  // Initialize hooks
  app.service(matchResultsPath).hooks({
    around: {
      all: [
        authenticate('jwt'),
        schemaHooks.resolveExternal(matchResultExternalResolver),
        schemaHooks.resolveResult(matchResultResolver)
      ]
    },
    before: {
      all: [
        schemaHooks.validateQuery(matchResultQueryValidator),
        schemaHooks.resolveQuery(matchResultQueryResolver)
      ],
      find: [],
      get: [],
      create: [
        skipIfDeletedByHook(),
        validateMatchResult(),
        populateUserTracking(),
        schemaHooks.validateData(matchResultDataValidator),
        schemaHooks.resolveData(matchResultDataResolver)
      ],
      patch: [
        skipIfDeletedByHook(),
        validateMatchResult(),
        populateUserTracking(),
        schemaHooks.validateData(matchResultPatchValidator),
        schemaHooks.resolveData(matchResultPatchResolver)
      ],
      remove: [
        skipIfDeletedByHook(),
        populateUserTracking()
      ]
    },
    after: {
      all: []
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    [matchResultsPath]: MatchResultsService
  }
}
