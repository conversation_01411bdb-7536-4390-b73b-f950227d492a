/**
 * Import players from CSV file with the following structure:
 * 
 * CSV Fields (tab/semicolon separated):
 * id, active, email, password, address, postcode, city, country, phone, birthdate, 
 * sex, firstname, lastname, latitude, longitude, equipment_category, activated, token, 
 * avatar, international, in_distance, distance, licenses, mini_map, vege, 
 * equipment_categories, language, last_login, current_login, push_tokens, mobile
 * 
 * Import Logic:
 * - Only imports records where active = 1 (skips inactive users)
 * - Creates User records only for activated users (activated != 0 and activated != \N)
 * - Always creates Player records (with or without associated User)
 * - Maps legacy IDs for data migration
 * - Handles various date formats and validates dates
 * - Skips fields not present in current schema (international, distance, mobile, etc.)
 */

import fs from 'fs';
import path from 'path';

import { parse } from 'csv-parse';

import { app } from '../src/app';
import type { Player, PlayerData } from '../src/services/players/players.schema';
import type { User, UserData } from '../src/services/users/users.schema';

// Define an interface for Feathers error type
interface FeathersError extends Error {
  code: number;
  className: string;
  data?: any[];
}

/**
 * Convert string to boolean
 */
const parseBool = (value: string | undefined): boolean | undefined => {
  if (value === undefined || value === '' || value === '\\N') return undefined;
  if (value === '0') return false;
  if (value === '1') return true;
  return undefined;
};

/**
 * Parse JSON field from string 
 * Returns the parsed JavaScript object/array for JSONB database storage
 */
const parseJsonField = (value: string | undefined): any | undefined => {
  if (!value || value === '\\N' || value === '-1' || value === '') return undefined;

  try {
    // Parse the JSON and return the JavaScript object/array for JSONB storage
    const parsed = JSON.parse(value);
    return parsed;
  } catch (error) {
    console.warn(`Failed to parse JSON value: "${value}", attempting to fix common issues`);

    // Handle common malformed JSON patterns
    let cleanedValue = value.trim();

    // Fix common JSON issues like {"1"} -> ["1"]
    if (cleanedValue.startsWith('{"') && cleanedValue.endsWith('"}') && !cleanedValue.includes(':')) {
      const innerValue = cleanedValue.slice(2, -2);
      cleanedValue = `["${innerValue}"]`;
    } else if (cleanedValue.startsWith('{') && cleanedValue.endsWith('}') && !cleanedValue.includes(':')) {
      const innerValue = cleanedValue.slice(1, -1);
      cleanedValue = `["${innerValue}"]`;
    }

    try {
      const parsed = JSON.parse(cleanedValue);
      return parsed;
    } catch (fallbackError) {
      console.error(`Complete failure parsing JSON value: ${value}`, fallbackError);
      return undefined;
    }
  }
};

/**
 * Parse date fields from DD.MM.YYYY to YYYY-MM-DD
 * Validates dates and returns null for invalid or unrealistic dates
 */
const parseDate = (value: string | undefined): string | undefined => {
  if (!value || value === '\\N') return undefined;

  let dateString: string;

  // Handle date format DD.MM.YYYY
  if (value.includes('.')) {
    const [day, month, year] = value.split('.');
    dateString = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  } else {
    dateString = value;
  }

  // Check for obviously invalid dates
  const invalidDates = ['0000-00-00', '0001-01-01', '1111-11-11'];
  if (invalidDates.includes(dateString)) {
    console.warn(`Invalid date detected and replaced with null: ${value} -> ${dateString}`);
    return undefined;
  }

  // Parse the date to validate it
  const parsedDate = new Date(dateString);

  // Check if the date is valid
  if (isNaN(parsedDate.getTime())) {
    console.warn(`Invalid date format detected and replaced with null: ${value} -> ${dateString}`);
    return undefined;
  }

  // Check if the date is before 1900-01-01
  const minDate = new Date('1900-01-01');
  if (parsedDate < minDate) {
    console.warn(`Date too early (before 1900) detected and replaced with null: ${value} -> ${dateString}`);
    return undefined;
  }

  // Check if the date is in the future (reasonable validation for birthdates)
  const today = new Date();
  if (parsedDate > today) {
    console.warn(`Future date detected and replaced with null: ${value} -> ${dateString}`);
    return undefined;
  }

  return dateString;
};

/**
 * Parse timestamp fields from DD.MM.YYYY HH:MM to ISO 8601 format (YYYY-MM-DDTHH:MM:SS.sssZ)
 * Includes validation for invalid dates
 */
const parseTimestamp = (value: string | undefined): string | undefined => {
  if (!value || value === '\\N') return undefined;

  console.log('parseTimestamp value:', value);

  // Handle date-time format DD.MM.YYYY HH:MM
  if (value.includes(' ') && value.includes('.')) {
    const [datePart, timePart] = value.split(' ');
    const dateParts = datePart.split('.');

    if (dateParts.length !== 3) {
      console.error(`Invalid date format: ${value}`);
      return undefined;
    }

    const [day, month, year] = dateParts;

    // Validate date parts
    if (!day || !month || !year || day.length === 0 || month.length === 0 || year.length === 0) {
      console.error(`Invalid date parts: ${value}`);
      return undefined;
    }

    // Check for obviously invalid dates before processing
    const dateString = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
    const invalidDates = ['0000-00-00', '0001-01-01', '1111-11-11'];
    if (invalidDates.includes(dateString)) {
      console.warn(`Invalid timestamp date detected and replaced with null: ${value} -> ${dateString}`);
      return undefined;
    }

    // Format time part - add seconds if missing
    let formattedTime = timePart || '00:00:00';
    if (!formattedTime.includes(':')) formattedTime += ':00:00';
    if (formattedTime.split(':').length === 2) formattedTime += ':00';

    // Validate time format
    const timeParts = formattedTime.split(':');
    if (timeParts.length !== 3) {
      console.error(`Invalid time format: ${value}`);
      return undefined;
    }

    // Create ISO 8601 date-time string
    const isoString = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T${formattedTime}.000Z`;

    // Validate the resulting date
    const date = new Date(isoString);
    if (isNaN(date.getTime())) {
      console.error(`Invalid date created: ${isoString} from ${value}`);
      return undefined;
    }

    // Check if the date is before 1900-01-01
    const minDate = new Date('1900-01-01');
    if (date < minDate) {
      console.warn(`Timestamp too early (before 1900) detected and replaced with null: ${value} -> ${isoString}`);
      return undefined;
    }

    console.log(`Parsed timestamp: ${value} -> ${isoString}`);
    return isoString;
  }

  // Try to parse as existing ISO date
  const date = new Date(value);
  if (!isNaN(date.getTime())) {
    // Still validate even existing ISO dates
    const minDate = new Date('1900-01-01');
    if (date < minDate) {
      console.warn(`Existing timestamp too early (before 1900) detected and replaced with null: ${value}`);
      return undefined;
    }
    return date.toISOString();
  }

  console.error(`Could not parse timestamp: ${value}`);
  return undefined;
};

/**
 * Parse string fields, handling special null values
 */
const parseString = (value: string | undefined): string | undefined => {
  if (!value || value === '\\N' || value === 'NULL' || value === 'null' || value === '') {
    return undefined;
  }
  return value.trim();
};

/**
 * Parse number fields, handling special values and ensuring type safety
 */
const parseNumber = (value: string | undefined): number | undefined => {
  if (!value || value === '\\N' || value === '-1' || value === '') return undefined;

  // Trim whitespace and handle common null values
  const trimmedValue = value.toString().trim();
  if (trimmedValue === '' || trimmedValue === '\\N' || trimmedValue === 'NULL' || trimmedValue === 'null') {
    return undefined;
  }

  const num = Number(trimmedValue);
  if (isNaN(num)) {
    console.warn(`Failed to parse number: "${value}" -> "${trimmedValue}"`);
    return undefined;
  }

  return num;
};

/**
 * Parse decimal fields (for latitude/longitude), handling comma as decimal separator
 */
const parseDecimal = (value: string | undefined): number | undefined => {
  if (!value || value === '\\N' || value === '') return undefined;

  // Replace comma with dot for decimal separator
  const normalizedValue = value.replace(',', '.');
  const num = parseFloat(normalizedValue);
  return !isNaN(num) ? num : undefined;
};

/**
 * Generate a random password for legacy users
 */
const generateRandomPassword = (): string => {
  return Math.random().toString(36).slice(-12) + Math.random().toString(36).slice(-12);
};

/**
 * Main import function for players from CSV
 */
async function importPlayers(csvFilePath: string) {
  const players = app.service('players');
  const users = app.service('users');

  // Ensure the path is absolute
  const filePath = path.isAbsolute(csvFilePath)
    ? csvFilePath
    : path.join(process.cwd(), csvFilePath);

  console.log(`Importing players from ${filePath}`);

  // First, read all records and sort them
  const allRecords: any[] = [];
  const parser = fs
    .createReadStream(filePath)
    .pipe(parse({
      delimiter: ';', // Semicolon-separated as per your file format
      columns: false, // Don't use headers, we'll map by position
      skip_empty_lines: true,
      relax_quotes: true,
      skip_records_with_empty_values: false // We want to handle empty values manually
    }));

  // Collect all records first
  for await (const record of parser) {
    if (!Array.isArray(record) || record.every(v => !v || v === '')) {
      continue;
    }
    allRecords.push(record);
  }

  // Sort records: activated users first (activated_at is not \N), then by email
  allRecords.sort((a, b) => {
    const aActivated = a[16] && a[16] !== '\\N';
    const bActivated = b[16] && b[16] !== '\\N';

    // Activated users first
    if (aActivated && !bActivated) return -1;
    if (!aActivated && bActivated) return 1;

    // Then sort by email (with email first)
    const aEmail = parseString(a[2]);
    const bEmail = parseString(b[2]);

    if (aEmail && !bEmail) return -1;
    if (!aEmail && bEmail) return 1;

    // Finally sort alphabetically by email
    return (aEmail || '').localeCompare(bEmail || '');
  });

  console.log(`Found ${allRecords.length} records to process`);

  let recordCount = 0;
  let importCount = 0;
  let errorCount = 0;
  const createdUserIds: number[] = [];
  const assignedToExistingUsers: number[] = [];
  const playersWithoutUsers: number[] = [];
  const errorLogPath = path.join(process.cwd(), 'import-players-errors.txt');
  // Clear previous error log
  fs.writeFileSync(errorLogPath, '');

  for (const record of allRecords) {
    recordCount++;

    // Skip completely empty rows
    if (!Array.isArray(record) || record.every(v => !v || v === '')) {
      continue;
    }

    try {
      // CSV columns mapping based on actual field order:
      // 0: id (legacy) -> Player.legacyId
      // 1: active -> User.isActive and Player.isActive  
      // 2: email -> User.email
      // 3: password (hashed) -> ignored, generate new random password
      // 4: address -> Player.address
      // 5: postcode -> Player.zipcode
      // 6: city -> Player.city
      // 7: country -> Player.country
      // 8: phone -> Player.phone
      // 9: birthdate (DD.MM.YYYY) -> Player.birthdate
      // 10: sex (M/F) -> Player.sex
      // 11: firstname -> Player.firstname
      // 12: lastname -> Player.lastname
      // 13: latitude -> Player.latitude
      // 14: longitude -> Player.longitude
      // 15: equipment_category -> ignored (not used)
      // 16: activated -> User.activatedAt and Player.activatedAt (boolean/timestamp)
      // 17: token -> ignored
      // 18: avatar -> User.avatar and Player.avatar
      // 19: international -> Player.international
      // 20: in_distance -> ignored/unused
      // 21: distance -> Player.distance (max distance)
      // 22: licenses -> Player.licenses (JSON)
      // 23: mini_map -> ignored/unused
      // 24: vege -> ignored/unused
      // 25: equipment_categories -> ignored (duplicate of field 15)
      // 26: language -> User.language
      // 27: last_login -> User.lastLogin
      // 28: current_login -> User.currentLogin
      // 29: push_tokens -> ignored/unused
      // 30: mobile -> Player.mobile

      const [
        legacyId, activeStr, emailRaw, hashedPassword, address, postcode, city, country, phone,
        birthdateStr, sex, firstname, lastname, latitudeStr, longitudeStr, equipmentCategoryIgnored,
        activatedStr, token, avatar, international, inDistance, distance, licenses,
        miniMap, vege, equipmentCategories, language, lastLogin, currentLogin, pushTokens, mobile
      ] = record;

      // Debug logging for key fields
      console.log(`Record #${recordCount} - Raw values: legacyId: "${legacyId}", active: "${activeStr}", email: "${emailRaw}", activated: "${activatedStr}"`);
      const parsedLegacyId = parseNumber(legacyId);
      console.log(`Record #${recordCount} - Parsed legacyId: ${parsedLegacyId}`);

      // Skip records where user is inactive (active = 0) - this is the main filter
      if (activeStr === '0') {
        console.log(`Skipping record #${recordCount} - user inactive (${emailRaw || 'no email'})`);
        continue;
      }

      const email = parseString(emailRaw);
      let userId: number | undefined;
      let isNewUser = false;

      // Handle user creation/assignment
      if (email) {
        // Check if user already exists
        try {
          const existingUsers = await users.find({
            query: { email: email, $limit: 1 }
          });

          if (existingUsers.total > 0) {
            // User exists, use existing user
            userId = existingUsers.data[0].id;
            assignedToExistingUsers.push(userId);
            console.log(`Using existing user for: ${email} (ID: ${userId})`);
          } else {
            // Create new user only if they were activated
            if (activatedStr && activatedStr !== '\\N' && activatedStr !== '0') {
              const userData: UserData = {
                email: email,
                password: generateRandomPassword(),
                isActive: parseBool(activeStr) ?? true,
                ...(parseString(language) ? { language: parseString(language) } : {})
              };

              // Add optional user fields
              const parsedAvatar = parseString(avatar);
              if (parsedAvatar && parsedAvatar !== 'woman_icon_2.jpg' && parsedAvatar !== 'man_icon_2.jpg') {
                userData.avatar = parsedAvatar;
              }

              // Handle activated field - could be boolean or timestamp
              const parsedActivated = parseString(activatedStr);
              if (parsedActivated === '1') {
                // If it's just '1', set activatedAt to current time
                userData.activatedAt = new Date().toISOString();
              } else {
                // Try to parse as timestamp
                const parsedActivatedAt = parseTimestamp(activatedStr);
                if (parsedActivatedAt) {
                  userData.activatedAt = parsedActivatedAt;
                }
              }

              // Handle login timestamps - these fields are not in User schema, skip them
              // if (lastLogin && lastLogin !== '\\N') {
              //   const parsedLastLogin = parseTimestamp(lastLogin);
              //   if (parsedLastLogin) {
              //     userData.lastLogin = parsedLastLogin;
              //   }
              // }

              // if (currentLogin && currentLogin !== '\\N') {
              //   const parsedCurrentLogin = parseTimestamp(currentLogin);
              //   if (parsedCurrentLogin) {
              //     userData.currentLogin = parsedCurrentLogin;
              //   }
              // }

              console.log(`Creating user for: ${email}`);
              const createdUser = await users.create(userData);
              userId = createdUser.id;
              createdUserIds.push(userId);
              isNewUser = true;
            } else {
              console.log(`Skipping user creation for ${email} - never activated, will create player only`);
            }
          }
        } catch (error) {
          console.error(`Error checking/creating user ${email}:`, error);
          // Continue without user - create player only
        }
      } else {
        // No email provided - we can now create a player without a user
        console.log(`No email provided for record #${recordCount} - creating player without user`);
        userId = undefined; // Will create player with null userId
      }

      // Create player data
      const playerData: PlayerData = {
        legacyId: parsedLegacyId,
        ...(userId ? { userId } : {}), // Only include userId if we have one
        isActive: parseBool(activeStr) ?? true, // Map CSV active to Player.isActive
      };

      // Add optional player fields
      if (parseString(address)) playerData.address = parseString(address);
      if (parseString(postcode)) playerData.zipcode = parseString(postcode);
      if (parseString(city)) playerData.city = parseString(city);
      if (parseString(country)) playerData.country = parseString(country);
      if (parseString(phone)) playerData.phone = parseString(phone);
      if (birthdateStr && birthdateStr !== '\\N') playerData.birthdate = parseDate(birthdateStr);

      // Keep sex field as M/F from CSV
      if (parseString(sex)) {
        playerData.sex = parseString(sex);
      }

      if (parseString(firstname)) playerData.firstname = parseString(firstname);
      if (parseString(lastname)) playerData.lastname = parseString(lastname);

      // Parse coordinates
      if (latitudeStr && latitudeStr !== '\\N') playerData.latitude = parseDecimal(latitudeStr);
      if (longitudeStr && longitudeStr !== '\\N') playerData.longitude = parseDecimal(longitudeStr);

      // Handle avatar
      const parsedAvatar = parseString(avatar);
      if (parsedAvatar && parsedAvatar !== 'woman_icon_2.jpg' && parsedAvatar !== 'man_icon_2.jpg') {
        playerData.avatar = parsedAvatar;
      }

      // Handle additional fields from new CSV structure
      // Note: international, distance, and mobile fields are not in Player schema
      // if (parseString(international)) playerData.international = parseBool(international);
      // if (parseString(distance)) playerData.distance = parseNumber(distance);
      // Handle licenses field - Parse JSON array
      if (parseString(licenses)) {
        console.log(`DEBUG: Raw licenses value: "${licenses}" (type: ${typeof licenses})`);
        try {
          playerData.licenses = JSON.parse(licenses);
          console.log(`DEBUG: Parsed licenses as array:`, playerData.licenses);
        } catch (e) {
          console.log(`DEBUG: Failed to parse licenses, treating as null:`, licenses);
          playerData.licenses = null;
        }
      } else {
        console.log(`DEBUG: No licenses data for player ${email || 'no email'}`);
        playerData.licenses = null;
      }
      // if (parseString(mobile)) playerData.mobile = parseString(mobile);

      // Handle activated field for player - Map CSV activated to Player.activatedAt
      if (activatedStr && activatedStr !== '\\N' && activatedStr !== '0') {
        const parsedActivated = parseString(activatedStr);
        if (parsedActivated === '1') {
          // If it's just '1', set activatedAt to current time
          playerData.activatedAt = new Date().toISOString();
        } else {
          // Try to parse as timestamp
          const parsedActivatedAt = parseTimestamp(activatedStr);
          if (parsedActivatedAt) {
            playerData.activatedAt = parsedActivatedAt;
          } else {
            console.warn(`Skipping invalid activatedAt for player ${email}: ${activatedStr}`);
          }
        }
      }

      // Since we no longer have createdAt, updatedAt, deletedAt in CSV, remove timestamp handling
      const additionalData: Partial<Player> = {};

      // Remove any undefined values to avoid schema validation issues
      Object.keys(additionalData).forEach(key => {
        if (additionalData[key as keyof Player] === undefined) {
          delete additionalData[key as keyof Player];
        }
      });

      // Debug: Log the complete playerData before sending to database
      console.log(`DEBUG: Complete playerData for record #${recordCount}:`, JSON.stringify(playerData, null, 2));

      // Create the player
      const createdPlayer = await players.create(playerData);

      // Apply additional timestamp data if available
      if (Object.keys(additionalData).length > 0) {
        await players.patch(createdPlayer.id, additionalData);
      }

      importCount++;
      if (!userId) {
        playersWithoutUsers.push(recordCount); // Track players without users
      }
      console.log(`Imported player #${importCount}: ${playerData.firstname} ${playerData.lastname} (${email || 'no user'}) - Legacy ID: ${parsedLegacyId}${userId && assignedToExistingUsers.includes(userId) ? ' [existing user]' : ''}${!userId ? ' [no user]' : ''}`);

    } catch (error: unknown) {
      errorCount++;
      console.error(`Error importing record #${recordCount}:`, error);
      // Log error to file
      const errorMsg = [
        `Error importing record #${recordCount}:`,
        JSON.stringify(error, null, 2),
        'Record data:',
        JSON.stringify(record, null, 2),
        '\n'
      ].join('\n');
      fs.appendFileSync(errorLogPath, errorMsg);
      // Enhanced error logging with proper type checking
      if (error && typeof error === 'object' && 'data' in error) {
        const feathersError = error as FeathersError;
        if (Array.isArray(feathersError.data) && feathersError.data.length > 0) {
          const details = [
            'Validation error details:',
            JSON.stringify(feathersError.data, null, 2),
            'Problematic record data:',
            JSON.stringify({
              email: Object.values(record)[2],
              firstname: Object.values(record)[11],
              lastname: Object.values(record)[12],
              legacyId: Object.values(record)[0]
            }, null, 2),
            '\n'
          ].join('\n');
          fs.appendFileSync(errorLogPath, details);
          console.error('Validation error details:', JSON.stringify(feathersError.data, null, 2));
          console.error('Problematic record data:', JSON.stringify({
            email: Object.values(record)[2],
            firstname: Object.values(record)[11],
            lastname: Object.values(record)[12],
            legacyId: Object.values(record)[0]
          }, null, 2));
        }
      }
    }
  }

  console.log(`Import complete: ${importCount} players imported successfully, ${errorCount} errors`);
  console.log(`Created ${createdUserIds.length} new users`);
  console.log(`Assigned ${assignedToExistingUsers.length} players to existing users`);
  console.log(`Created ${playersWithoutUsers.length} players without associated users`);

  // Note: Users created during import will have random passwords
  if (createdUserIds.length > 0) {
    console.log('Note: All newly created users have been assigned random passwords and will need to reset them');
  }
  if (playersWithoutUsers.length > 0) {
    console.log('Note: Players without associated users can be linked to users later if needed');
  }
}

// If this script is run directly (not imported)
if (require.main === module) {
  const csvFilePath = process.argv[2];
  if (!csvFilePath) {
    console.error('Error: No CSV file path provided');
    console.log('Usage: pnpm exec ts-node scripts/import-players.ts <path/to/csv/file>');
    process.exit(1);
  }

  // Run the import
  importPlayers(csvFilePath)
    .then(() => process.exit(0))
    .catch(error => {
      console.error('Import failed:', error);
      process.exit(1);
    });
}

export { importPlayers };
