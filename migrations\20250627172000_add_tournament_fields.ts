import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
    await knex.schema.alterTable('tournaments', (table) => {
        table.jsonb('styleDivisions')
        table.jsonb('ageDivisions')
        table.boolean('forWomen')
        table.boolean('forMen')
        table.boolean('isOpen')
        table.integer('federationId').references('id').inTable('federations')
        table.boolean('licenseRequired')
        table.boolean('international')
        table.timestamp('completedAt')
        table.string('rulesSettings')
        table.integer('totalRounds')
        table.integer('minRounds')
        table.jsonb('agenda')
        table.jsonb('generalScore')
        table.string('competitionLevel')
        table.boolean('yearly')
    })
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.alterTable('tournaments', (table) => {
        table.dropColumn('styleDivisions')
        table.dropColumn('ageDivisions')
        table.dropColumn('forWomen')
        table.dropColumn('forMen')
        table.dropColumn('isOpen')
        table.dropColumn('federationId')
        table.dropColumn('licenseRequired')
        table.dropColumn('international')
        table.dropColumn('completedAt')
        table.dropColumn('rulesSettings')
        table.dropColumn('totalRounds')
        table.dropColumn('minRounds')
        table.dropColumn('agenda')
        table.dropColumn('generalScore')
        table.dropColumn('competitionLevel')
        table.dropColumn('yearly')
    })
}
