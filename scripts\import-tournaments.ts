import fs from 'fs';
import path from 'path';

import { parse } from 'csv-parse';

import { app } from '../src/app';
import type { Tournament, TournamentData } from '../src/services/tournaments/tournaments.schema';

// Define an interface for Feathers error type
interface FeathersError extends Error {
    code: number;
    className: string;
    data?: any[];
}

/**
 * Convert string to boolean
 */
const parseBool = (value: string | undefined): boolean | undefined => {
    if (value === undefined || value === '' || value === '\\N') return undefined;
    if (value === '0') return false;
    if (value === '1') return true;
    return undefined;
};

/**
 * Parse JSON field from string 
 * Returns the original JSON string if valid, undefined if invalid/empty
 */
const parseJsonField = (value: string | undefined): string | undefined => {
    if (!value || value === '\\N' || value === '-1') return undefined;

    try {
        // Just validate that it's valid JSON, but return the original string
        JSON.parse(value);
        return value;
    } catch (error) {
        console.error(`Error parsing JSON value: ${value}`, error);
        return undefined;
    }
};

/**
 * Parse date fields from DD.MM.YYYY to YYYY-MM-DD
 */
const parseDate = (value: string | undefined): string | undefined => {
    if (!value || value === '\\N') return undefined;

    // Handle date format DD.MM.YYYY
    if (value.includes('.')) {
        const [day, month, year] = value.split('.');
        return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
    }

    return value;
};

/**
 * Parse timestamp fields from DD.MM.YYYY HH:MM or YYYY-MM-DD HH:MM:SS to ISO 8601 format (YYYY-MM-DDTHH:MM:SS.sssZ)
 */
const parseTimestamp = (value: string | undefined): string | undefined => {
    if (!value || value === '\\N') return undefined;

    console.log('parseTimestamp value:', value);

    // Handle date-time format DD.MM.YYYY HH:MM
    if (value.includes(' ') && value.includes('.')) {
        const [datePart, timePart] = value.split(' ');
        const [day, month, year] = datePart.split('.');

        // Format time part - add seconds if missing
        let formattedTime = timePart;
        if (!formattedTime.includes(':')) formattedTime += ':00';
        if (formattedTime.split(':').length === 2) formattedTime += ':00';

        // Create ISO 8601 date-time string
        return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T${formattedTime}.000Z`;
    }

    // Handle date-time format YYYY-MM-DD HH:MM:SS
    if (value.includes(' ') && value.includes('-')) {
        const [datePart, timePart] = value.split(' ');

        // Format time part - add seconds if missing
        let formattedTime = timePart;
        if (!formattedTime.includes(':')) formattedTime += ':00';
        if (formattedTime.split(':').length === 2) formattedTime += ':00';

        // Create ISO 8601 date-time string
        return `${datePart}T${formattedTime}.000Z`;
    }

    return value;
};

/**
 * Parse string fields, handling special null values
 */
const parseString = (value: string | undefined): string | undefined => {
    if (!value || value === '\\N' || value === 'NULL' || value === 'null' || value === '') {
        return undefined;
    }
    return value.trim();
};

/**
 * Parse number fields, handling special values and ensuring type safety
 */
const parseNumber = (value: string | undefined): number | undefined => {
    if (!value || value === '\\N' || value === '-1' || value === '') return undefined;

    const num = Number(value);
    if (isNaN(num)) {
        console.warn(`Failed to parse number: "${value}"`);
        return undefined;
    }

    return num;
};

/**
 * Main import function for tournaments from CSV
 */
async function importTournaments(csvFilePath: string) {
    const tournaments = app.service('tournaments');
    const federations = app.service('federations');

    // Build federation lookup map: legacyId -> id
    const federationList = await federations.find({ paginate: false });
    const federationLookup = new Map<number, number>();

    if (Array.isArray(federationList)) {
        for (const federation of federationList) {
            if (federation.legacyId) {
                federationLookup.set(federation.legacyId, federation.id);
            }
        }
    }

    console.log(`Loaded ${federationLookup.size} federations for lookup`);

    // Ensure the path is absolute
    const filePath = path.isAbsolute(csvFilePath)
        ? csvFilePath
        : path.join(process.cwd(), csvFilePath);

    console.log(`Importing tournaments from ${filePath}`);

    const parser = fs
        .createReadStream(filePath)
        .pipe(parse({
            delimiter: ';', // Use semicolon as delimiter
            columns: true, // Use headers from CSV
            skip_empty_lines: true,
            relax_quotes: true,
            skip_records_with_empty_values: true
        }));

    let recordCount = 0;
    let importCount = 0;
    let errorCount = 0;

    for await (const record of parser) {
        recordCount++;

        // Debug: Log the first record to see column names
        if (recordCount === 1) {
            console.log('CSV Headers/Keys:', Object.keys(record));
            console.log('First key details:', {
                'first_key': Object.keys(record)[0],
                'first_key_length': Object.keys(record)[0]?.length,
                'first_key_charCodes': Object.keys(record)[0]?.split('').map(c => c.charCodeAt(0))
            });
            console.log('First few values:', {
                'quoted_id': record['"id"'],
                'id': record.id,
                'first_key_value': record[Object.keys(record)[0]],
                name: record.name,
                active: record.active
            });
        }

        // Skip completely empty rows
        if (Object.values(record).every(v => !v || v === '')) {
            continue;
        }

        try {
            // Lookup organizer by legacyId if organizer_id is present
            let resolvedOrganizerId: number | undefined = undefined;
            if (record.organizer_id) {
                const organizersService = app.service('organizers');
                const found = await organizersService.find({
                    query: { legacyId: parseNumber(record.organizer_id) },
                    paginate: false
                });
                if (Array.isArray(found) && found.length > 0) {
                    resolvedOrganizerId = found[0].id;
                    console.log('found organizer id:', resolvedOrganizerId)
                }
            }

            // Lookup federation by legacyId if federation is present
            let resolvedFederationId: number | undefined = undefined;
            if (record.federation && record.federation !== '\\N' && record.federation !== '-1') {
                const federationLegacyId = parseNumber(record.federation);
                if (federationLegacyId) {
                    resolvedFederationId = federationLookup.get(federationLegacyId);
                    if (resolvedFederationId) {
                        console.log('found federation id:', resolvedFederationId, 'for legacyId:', federationLegacyId);
                    } else {
                        console.warn('Federation not found for legacyId:', federationLegacyId);
                    }
                }
            }

            // Handle the quoted "id" field name issue - BOM and quotes can cause issues
            const firstKey = Object.keys(record)[0];
            let rawId = record[firstKey]; // Always use the first key which should be the ID

            const parsedLegacyId = parseNumber(rawId);
            console.log(`Record #${recordCount} - Using key "${firstKey}" -> Raw ID: "${rawId}" -> Parsed legacyId: ${parsedLegacyId}`);

            if (!parsedLegacyId) {
                console.warn(`No valid legacyId found for record ${recordCount}, skipping`);
                errorCount++;
                continue;
            }

            // Skip if organizer is required but not found
            if (!resolvedOrganizerId) {
                console.warn(`No valid organizerId found for record ${recordCount}, skipping`);
                errorCount++;
                continue;
            }

            const tournamentData: TournamentData = {
                legacyId: parsedLegacyId,
                name: parseString(record.name) || 'Unnamed Tournament',
                isActive: parseBool(record.active) ?? true,
                organizerId: resolvedOrganizerId,
                description: parseString(record.description)
            };

            // Then add the additional data as a patch
            const additionalData: Partial<Tournament> = {
                styleDivisions: parseJsonField(record.equipment_categories),
                ageDivisions: parseJsonField(record.age_categories),
                forWomen: parseBool(record.for_women),
                forMen: parseBool(record.for_men),
                isOpen: parseBool(record.open),
                federationId: resolvedFederationId,
                licenseRequired: parseBool(record.license_required),
                international: parseBool(record.international),
                completedAt: parseTimestamp(record.completed),
                rulesSettings: parseString(record.rules_settings),
                totalRounds: parseNumber(record.competition_amount),
                minRounds: parseNumber(record.competition_amount_counted),
                agenda: parseJsonField(record.agenda),
                generalScore: parseJsonField(record.general_score),
                competitionLevel: parseString(record.competition_level),
                yearly: parseBool(record.yearly),
                createdAt: parseTimestamp(record.create_date)
            };

            // Special handling for values that caused validation errors
            if (additionalData.completedAt === undefined) {
                delete additionalData.completedAt;
            }

            if (additionalData.federationId === undefined) {
                delete additionalData.federationId;
            }

            // Remove any undefined values to avoid schema validation issues
            Object.keys(additionalData).forEach(key => {
                if (additionalData[key as keyof Tournament] === undefined) {
                    delete additionalData[key as keyof Tournament];
                }
            });

            // Debug for fields that caused validation errors
            if (record.completed) {
                console.log(`Record #${recordCount} - completedAt field: ${record.completed} -> ${additionalData.completedAt || 'removed'}`);
            }

            if (record.federation) {
                console.log(`Record #${recordCount} - federationId: ${record.federation} -> ${additionalData.federationId || 'removed'}`);
            }

            // Create the tournament with required fields first, then update with additional data
            console.log(`Creating tournament with data:`, {
                legacyId: tournamentData.legacyId,
                name: tournamentData.name,
                isActive: tournamentData.isActive,
                organizerId: tournamentData.organizerId
            });

            const createdTournament = await tournaments.create(tournamentData);
            console.log(`Created tournament:`, {
                id: createdTournament.id,
                legacyId: createdTournament.legacyId,
                name: createdTournament.name
            });

            if (Object.keys(additionalData).length > 0) {
                const patchedTournament = await tournaments.patch(createdTournament.id, additionalData);
                console.log(`Patched tournament:`, {
                    id: patchedTournament.id,
                    legacyId: patchedTournament.legacyId,
                    name: patchedTournament.name
                });
            }

            importCount++;
            console.log(`Imported tournament #${importCount}: ${tournamentData.name}`);
        } catch (error: unknown) {
            errorCount++;
            console.error(`Error importing record #${recordCount}:`, error);

            // Enhanced error logging with proper type checking
            if (error && typeof error === 'object' && 'data' in error) {
                const feathersError = error as FeathersError;
                if (Array.isArray(feathersError.data) && feathersError.data.length > 0) {
                    console.error('Validation error details:', JSON.stringify(feathersError.data, null, 2));
                    console.error('Problematic record data:', JSON.stringify({
                        name: record.name,
                        completed: record.completed,
                        federation: record.federation,
                        active: record.active,
                        organizerId: record.organizer_id,
                        createDate: record.create_date
                    }, null, 2));
                }
            }
        }
    }

    console.log(`Import complete: ${importCount} tournaments imported successfully, ${errorCount} errors`);
}

// If this script is run directly (not imported)
if (require.main === module) {
    const csvFilePath = process.argv[2];
    if (!csvFilePath) {
        console.error('Error: No CSV file path provided');
        console.log('Usage: pnpm exec ts-node scripts/import-tournaments.ts <path/to/csv/file>');
        process.exit(1);
    }

    // Run the import
    importTournaments(csvFilePath)
        .then(() => process.exit(0))
        .catch(error => {
            console.error('Import failed:', error);
            process.exit(1);
        });
}

export { importTournaments };
