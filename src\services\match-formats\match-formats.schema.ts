// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, virtual } from '@feathersjs/schema'
import { Type, getValidator, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'

import type { HookContext } from '../../declarations'
import { dataValidator, queryValidator } from '../../validators'
import type { MatchFormatService } from './match-formats.class'

// Main data model schema
export const matchFormatSchema = Type.Object(
  {
    id: Type.Number(),
    legacyId: Type.Optional(Type.Number()),
    active: Type.Boolean(),
    name: Type.String(),
    numberOfShots: Type.Optional(Type.Number()),
    typesOfTargets: Type.Optional(Type.String()),
    targets: Type.Optional(Type.Number()),
    scoring: Type.Optional(Type.Unknown()), // JSONB array of scoring values
    matchType: Type.Optional(Type.String()),
    federationId: Type.Optional(Type.Number()),
    federation: Type.Optional(Type.Unknown()), // Virtual field for federation relation
    createdAt: Type.Optional(Type.String({ format: 'date-time' })),
    updatedAt: Type.Optional(Type.String({ format: 'date-time' })),
    deletedAt: Type.Optional(Type.String({ format: 'date-time' })),
    createdBy: Type.Optional(Type.Number()),
    updatedBy: Type.Optional(Type.Number()),
    deletedBy: Type.Optional(Type.Number())
  },
  { $id: 'MatchFormat', additionalProperties: false }
)
export type MatchFormat = Static<typeof matchFormatSchema>
export const matchFormatValidator = getValidator(matchFormatSchema, dataValidator)
export const matchFormatResolver = resolve<MatchFormat, HookContext<MatchFormatService>>({
  // Add a virtual field to load the related federation
  federation: virtual(async (matchFormat: MatchFormat, context: HookContext<MatchFormatService>) => {
    if (matchFormat.federationId) {
      return context.app.service('federations').get(matchFormat.federationId)
    }
    return undefined
  })
})

export const matchFormatExternalResolver = resolve<MatchFormat, HookContext<MatchFormatService>>({})

// Schema for creating new entries
export const matchFormatDataSchema = Type.Pick(
  matchFormatSchema,
  [
    'legacyId',
    'active',
    'name',
    'numberOfShots',
    'typesOfTargets',
    'targets',
    'scoring',
    'matchType',
    'federationId'
  ],
  {
    $id: 'MatchFormatData'
  }
)
export type MatchFormatData = Static<typeof matchFormatDataSchema>
export const matchFormatDataValidator = getValidator(matchFormatDataSchema, dataValidator)
export const matchFormatDataResolver = resolve<MatchFormat, HookContext<MatchFormatService>>({
  createdAt: async () => {
    return new Date().toISOString()
  },
  updatedAt: async () => {
    return new Date().toISOString()
  }
})

// Schema for updating existing entries
export const matchFormatPatchSchema = Type.Partial(matchFormatSchema, {
  $id: 'MatchFormatPatch'
})
export type MatchFormatPatch = Static<typeof matchFormatPatchSchema>
export const matchFormatPatchValidator = getValidator(matchFormatPatchSchema, dataValidator)
export const matchFormatPatchResolver = resolve<MatchFormat, HookContext<MatchFormatService>>({
  updatedAt: async () => {
    return new Date().toISOString()
  }
})

// Schema for allowed query properties
export const matchFormatQueryProperties = Type.Pick(
  matchFormatSchema,
  [
    'id',
    'legacyId',
    'active',
    'name',
    'numberOfShots',
    'typesOfTargets',
    'targets',
    'matchType',
    'federationId'
  ]
)
export const matchFormatQuerySchema = Type.Intersect(
  [
    querySyntax(matchFormatQueryProperties),
    // Add additional query properties here
    Type.Object({}, { additionalProperties: false })
  ],
  { additionalProperties: false }
)
export type MatchFormatQuery = Static<typeof matchFormatQuerySchema>
export const matchFormatQueryValidator = getValidator(matchFormatQuerySchema, queryValidator)
export const matchFormatQueryResolver = resolve<MatchFormatQuery, HookContext<MatchFormatService>>({})
