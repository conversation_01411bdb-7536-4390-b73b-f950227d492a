import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('matches', table => {
    table.renameColumn('text', 'name')
    table.boolean('isActive').notNullable().defaultTo(true)
    table.string('country')
    table.string('city')
    table.string('postcode')
    table.string('address')
    table.string('phone')
    table.string('email')
    table.date('startDate')
    table.date('endDate')
    table.timestamp('registrationEnds')
    table.text('description')
    table.string('photo')
    table.string('matchType')
    table.jsonb('equipmentCategories')
    table.jsonb('ageCategories')
    table.boolean('forWomen')
    table.boolean('forMen')
    table.string('federation')
    table.boolean('licenseRequired')
    table.integer('organizerId')
    table.integer('maxPlayersAmount')
    table.jsonb('payments')
    table.string('competitionLevel')
    table.boolean('international')
    table.boolean('withoutLimits')
    table.date('public')
    table.boolean('completed')
    table.decimal('latitude', 9, 6)
    table.decimal('longitude', 9, 6)
    table.jsonb('agenda')
    table.string('currency')
    table.jsonb('judges')
    table.boolean('registrationFinished')
    table.jsonb('attachments')
    table.timestamp('createdAt').defaultTo(knex.fn.now())
    table.integer('tournamentId')
    table.boolean('yearly')
  })
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('matches', table => {
    table.renameColumn('name', 'text')
    table.dropColumn('isActive')
    table.dropColumn('country')
    table.dropColumn('city')
    table.dropColumn('postcode')
    table.dropColumn('address')
    table.dropColumn('phone')
    table.dropColumn('email')
    table.dropColumn('startDate')
    table.dropColumn('endDate')
    table.dropColumn('registrationEnds')
    table.dropColumn('description')
    table.dropColumn('photo')
    table.dropColumn('matchType')
    table.dropColumn('equipmentCategories')
    table.dropColumn('ageCategories')
    table.dropColumn('forWomen')
    table.dropColumn('forMen')
    table.dropColumn('federation')
    table.dropColumn('licenseRequired')
    table.dropColumn('organizerId')
    table.dropColumn('maxPlayersAmount')
    table.dropColumn('payments')
    table.dropColumn('competitionLevel')
    table.dropColumn('international')
    table.dropColumn('withoutLimits')
    table.dropColumn('public')
    table.dropColumn('completed')
    table.dropColumn('latitude')
    table.dropColumn('longitude')
    table.dropColumn('agenda')
    table.dropColumn('currency')
    table.dropColumn('judges')
    table.dropColumn('registrationFinished')
    table.dropColumn('attachments')
    table.dropColumn('createdAt')
    table.dropColumn('tournamentId')
    table.dropColumn('yearly')
  })
}

