import assert from 'assert'

import { app } from '../../../src/app'
import { createTestOrganizer } from '../../fixtures/create-test-organizer'

describe('Match-Organizer required validation', () => {
  const matchesService = app.service('matches')
  const organizersService = app.service('organizers')
  const usersService = app.service('users')

  let user: any
  let organizer: any
  let userParams: any

  before(async () => {
    // Create a test user
    user = await usersService.create({
      email: `test-match-organizer-required-${Date.now()}@example.com`,
      password: 'supersecret'
    })

    // Create authentication params
    const { accessToken } = await app.service('authentication').create({
      strategy: 'local',
      email: user.email,
      password: 'supersecret'
    })

    userParams = {
      provider: 'rest',
      authentication: {
        strategy: 'jwt',
        accessToken
      },
      user
    }

    // Create a test organizer
    organizer = await createTestOrganizer(user.id, userParams)
  })

  after(async () => {
    try {
      // Clean up in the correct order due to foreign key constraints
      if (organizer) {
        await organizersService.remove(organizer.id, userParams)
      }

      if (user) {
        await usersService.remove(user.id)
      }
    } catch (error) {
      console.error('Error cleaning up test data:', error)
    }
  })

  it('requires an organizer when creating a match', async () => {
    try {
      // Try to create a match without an organizer
      await matchesService.create({
        name: 'Test Match Without Organizer',
        isActive: true,
        description: 'A test match that should fail validation',
        equipmentCategories: JSON.stringify(['recurve', 'compound']),
        ageCategories: JSON.stringify(['senior', 'junior']),
        forMen: true,
        forWomen: true,
        maxPlayersAmount: 10
        // organizerId is intentionally omitted
      }, userParams)

      // If we get here, the test should fail
      assert.fail('Should have thrown an error for missing organizer')
    } catch (error: any) {
      // Verify that we got the expected error
      assert.strictEqual(error.name, 'BadRequest', 'Error should be a BadRequest')
      assert.strictEqual(error.message, 'Organizer is required when creating a match', 'Error message should indicate organizer is required')
    }
  })

  it('validates that the organizer exists', async () => {
    try {
      // Try to create a match with a non-existent organizer
      await matchesService.create({
        name: 'Test Match With Invalid Organizer',
        isActive: true,
        description: 'A test match that should fail validation',
        equipmentCategories: JSON.stringify(['recurve', 'compound']),
        ageCategories: JSON.stringify(['senior', 'junior']),
        forMen: true,
        forWomen: true,
        maxPlayersAmount: 10,
        organizerId: 99999 // Non-existent organizer ID
      }, userParams)

      // If we get here, the test should fail
      assert.fail('Should have thrown an error for non-existent organizer')
    } catch (error: any) {
      // Verify that we got the expected error
      assert.strictEqual(error.name, 'BadRequest', 'Error should be a BadRequest')
      assert.strictEqual(error.message, 'The selected organizer does not exist', 'Error message should indicate organizer does not exist')
    }
  })

  it('successfully creates a match with a valid organizer', async () => {
    try {
      // Create a match with a valid organizer
      const match = await matchesService.create({
        name: 'Test Match With Valid Organizer',
        isActive: true,
        description: 'A test match that should pass validation',
        equipmentCategories: JSON.stringify(['recurve', 'compound']),
        ageCategories: JSON.stringify(['senior', 'junior']),
        forMen: true,
        forWomen: true,
        maxPlayersAmount: 10,
        organizerId: organizer.id // Valid organizer ID
      }, userParams)

      // Verify the match was created with the correct organizer
      assert.ok(match, 'Match should be created')
      assert.strictEqual(match.organizerId, organizer.id, 'Match should have the correct organizer ID')

      // Clean up the match
      await matchesService.remove(match.id, userParams)
    } catch (error: any) {
      assert.fail(`Should not have thrown an error: ${error.message}`)
    }
  })
})
