# Tournaments Import Script

This script imports tournament data from CSV files into the tournaments table.

## Prerequisites

- Ensure organizers are imported first (tournaments require organizer references)
- Ensure federations are imported first (optional, but recommended if tournaments reference federations)
- Run the migration to add new tournament fields: `pnpm run migrate:latest`

## Usage

### Windows
```bash
scripts\import-tournaments.bat path\to\tournaments.csv
```

### Unix/Linux/Mac
```bash
./scripts/import-tournaments.sh path/to/tournaments.csv
```

### Direct TypeScript execution
```bash
pnpm exec ts-node scripts/import-tournaments.ts path/to/tournaments.csv
```

## CSV Format

The script expects a semicolon-delimited CSV file with the following columns:

### Required Columns
- `id` - Legacy tournament ID (used for referencing)
- `name` - Tournament name
- `organizer_id` - Legacy organizer ID (must exist in organizers table)

### Optional Columns
- `active` - Tournament active status (1/0)
- `equipment_categories` - JSON array of equipment categories (mapped to styleDivisions)
- `age_categories` - JSON array of age categories (mapped to ageDivisions)
- `for_women` - Tournament open for women (1/0)
- `for_men` - Tournament open for men (1/0)
- `open` - Tournament is open (1/0)
- `federation` - Legacy federation ID
- `license_required` - License required to participate (1/0)
- `international` - International tournament (1/0)
- `completed` - Tournament completion date/time
- `create_date` - Tournament creation date/time
- `rules_settings` - JSON object with rules settings
- `competition_amount` - Number of competitions
- `competition_amount_counted` - Number of competitions counted
- `agenda` - JSON array/object with agenda information
- `general_score` - JSON object with general scoring information
- `competition_level` - Competition level (e.g., "FRIENDLY")
- `yearly` - Yearly tournament (1/0)

## Field Mappings

The following CSV fields are mapped to database fields with naming conversions:

| CSV Field | Database Field | Type | Notes |
|-----------|----------------|------|-------|
| `equipment_categories` | `styleDivisions` | JSONB | Equipment categories converted to style divisions |
| `age_categories` | `ageDivisions` | JSONB | Age categories converted to age divisions |
| `rules_settings` | `rulesSettings` | STRING | Rules settings as string |
| `competition_amount` | `totalRounds` | INTEGER | Total number of rounds |
| `competition_amount_counted` | `minRounds` | INTEGER | Minimum rounds required |
| `general_score` | `generalScore` | JSONB | General scoring information |
| `competition_level` | `competitionLevel` | STRING | Competition level |
| `for_women` | `forWomen` | BOOLEAN | Open for women |
| `for_men` | `forMen` | BOOLEAN | Open for men |
| `license_required` | `licenseRequired` | BOOLEAN | License requirement |

## Error Handling

The script includes comprehensive error handling:

- Validates required fields (legacyId, name, organizerId)
- Handles `\N` values as null/undefined
- Validates JSON fields before storing
- Logs detailed validation errors
- Continues processing after individual record failures
- Provides summary statistics at completion

## Data Validation

- All JSON fields are validated for proper format before storage
- Foreign key lookups are performed for organizers and federations
- Boolean fields accept `0`, `1`, or `\N` values
- Date/timestamp fields support multiple formats
- String fields are trimmed and null values are handled consistently

## Output

The script provides detailed logging including:
- CSV parsing information
- Field mapping details
- Foreign key resolution results
- Individual record processing status
- Error details for failed records
- Final import statistics

## Notes

- The script uses the same robust CSV parsing and error handling patterns as the matches import
- All new fields follow camelCase naming conventions
- JSONB fields are properly stringified before database storage
- The script handles BOM and quoted field names in CSV headers
- Federation and organizer lookups are performed using legacy IDs
