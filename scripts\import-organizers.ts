import fs from 'fs';
import path from 'path';

import { parse } from 'csv-parse';

import { app } from '../src/app';

interface FeathersError extends Error {
    code: number;
    className: string;
    data?: any[];
}

const parseBool = (value: string | undefined): boolean | undefined => {
    if (value === undefined || value === '' || value === '\\N') return undefined;
    if (value === '0') return false;
    if (value === '1') return true;
    return undefined;
};

const parseString = (value: string | undefined): string | undefined => {
    if (!value || value === '\\N' || value === 'NULL' || value === 'null' || value === '') {
        return undefined;
    }
    return value.trim();
};

const parseNumber = (value: string | undefined): number | undefined => {
    if (!value || value === '\\N' || value === '-1' || value === '') return undefined;
    const num = Number(value);
    return !isNaN(num) ? num : undefined;
};

const parseDecimal = (value: string | undefined): number | undefined => {
    if (!value || value === '\\N' || value === '') return undefined;
    const normalizedValue = value.replace(',', '.');
    const num = parseFloat(normalizedValue);
    return !isNaN(num) ? num : undefined;
};

const parseTimestamp = (value: string | undefined): string | undefined => {
    if (!value || value === '\\N') return undefined;
    if (value.includes(' ') && value.includes('.')) {
        const [datePart, timePart] = value.split(' ');
        const dateParts = datePart.split('.');
        if (dateParts.length !== 3) return undefined;
        const [day, month, year] = dateParts;
        let formattedTime = timePart || '00:00:00';
        if (!formattedTime.includes(':')) formattedTime += ':00:00';
        if (formattedTime.split(':').length === 2) formattedTime += ':00';
        const timeParts = formattedTime.split(':');
        if (timeParts.length !== 3) return undefined;
        const isoString = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T${formattedTime}.000Z`;
        const date = new Date(isoString);
        if (isNaN(date.getTime())) return undefined;
        return isoString;
    }
    const date = new Date(value);
    if (!isNaN(date.getTime())) return date.toISOString();
    return undefined;
};

const generateRandomPassword = (): string => {
    return Math.random().toString(36).slice(-12) + Math.random().toString(36).slice(-12);
};

async function importOrganizers(csvFilePath: string) {
    const organizers = app.service('organizers');
    const users = app.service('users');
    const filePath = path.isAbsolute(csvFilePath) ? csvFilePath : path.join(process.cwd(), csvFilePath);
    console.log(`Importing organizers from ${filePath}`);
    const parser = fs.createReadStream(filePath).pipe(parse({
        delimiter: ';',
        columns: false,
        skip_empty_lines: true,
        relax_quotes: true,
        skip_records_with_empty_values: false
    }));
    let recordCount = 0;
    let importCount = 0;
    let errorCount = 0;
    const createdUserIds: number[] = [];
    const errorLogPath = path.join(process.cwd(), 'import-organizers-errors.txt');
    fs.writeFileSync(errorLogPath, '');
    for await (const record of parser) {
        recordCount++;
        if (!Array.isArray(record) || record.every(v => !v || v === '')) continue;
        try {
            // CSV columns:
            // 0: id (legacy)
            // 1: active (1/0)
            // 2: email
            // 3: password (hashed, ignore)
            // 4: address
            // 5: postcode
            // 6: city
            // 7: country
            // 8: phone
            // 9: name
            // 10: latitude
            // 11: longitude
            // 12: contact_person
            // 13: nip_regon -> vatId
            // 14: bank_account
            // 15: about
            // 16: avatar
            // 17: verified (1/0)
            // 18: activated (date)
            // 19: token (ignore)
            // 20: language
            const [
                legacyId, activeStr, email, _hashedPassword, address, postcode, city, country, phone,
                name, latitudeStr, longitudeStr, contactPerson, nipRegon, bankAccount, about, avatar,
                verifiedStr, activatedAtStr, _token, language
            ] = record;
            if (!parseString(email)) {
                console.log(`Skipping record #${recordCount} - no email`);
                continue;
            }
            if (!activatedAtStr || activatedAtStr === '\\N') {
                console.log(`Skipping record #${recordCount} - never activated (${email})`);
                continue;
            }
            if (activeStr === '0') {
                console.log(`Skipping record #${recordCount} - inactive (${email})`);
                continue;
            }
            const userData: any = {
                email,
                password: generateRandomPassword(),
                isActive: parseBool(activeStr) ?? true,
            };
            if (parseString(avatar)) userData.avatar = parseString(avatar);
            if (activatedAtStr && activatedAtStr !== '\\N') {
                const parsedActivatedAt = parseTimestamp(activatedAtStr);
                if (parsedActivatedAt) userData.activatedAt = parsedActivatedAt;
            }
            if (parseString(language)) userData.language = parseString(language);
            const createdUser = await users.create(userData);
            createdUserIds.push(createdUser.id);
            const organizerData: any = {
                userId: createdUser.id,
                name: parseString(name) || 'Unnamed Organizer',
                isActive: parseBool(activeStr) ?? true,
                legacyId: parseNumber(legacyId),
            };
            if (parseString(address)) organizerData.address = parseString(address);
            if (parseString(postcode)) organizerData.zipcode = parseString(postcode);
            if (parseString(city)) organizerData.city = parseString(city);
            if (parseString(country)) organizerData.country = parseString(country);
            if (parseString(phone)) organizerData.phone = parseString(phone);
            if (latitudeStr && latitudeStr !== '\\N') organizerData.latitude = parseDecimal(latitudeStr);
            if (longitudeStr && longitudeStr !== '\\N') organizerData.longitude = parseDecimal(longitudeStr);
            if (parseString(contactPerson)) organizerData.contactPerson = parseString(contactPerson);
            if (parseString(nipRegon)) organizerData.vatId = parseString(nipRegon);
            if (parseString(bankAccount)) organizerData.bankAccount = parseString(bankAccount);
            if (parseString(about)) organizerData.about = parseString(about);
            if (avatar && avatar !== '\\N') organizerData.avatar = avatar;
            if (verifiedStr && verifiedStr !== '\\N') organizerData.verified = parseBool(verifiedStr);

            // Remove undefined fields
            Object.keys(organizerData).forEach(key => {
                if (organizerData[key] === undefined) delete organizerData[key];
            });
            const createdOrganizer = await organizers.create(organizerData);
            importCount++;
            console.log(`Imported organizer #${importCount}: ${name} (${email}) - Legacy ID: ${legacyId}`);
        } catch (error: unknown) {
            errorCount++;
            console.error(`Error importing record #${recordCount}:`, error);
            const errorMsg = [
                `Error importing record #${recordCount}:`,
                JSON.stringify(error, null, 2),
                'Record data:',
                JSON.stringify(record, null, 2),
                '\n'
            ].join('\n');
            fs.appendFileSync(errorLogPath, errorMsg);
        }
    }
    console.log(`Import complete: ${importCount} organizers imported successfully, ${errorCount} errors`);
    console.log(`Created ${createdUserIds.length} new users`);
    console.log('Note: All imported users have been assigned random passwords and will need to reset them');
}

if (require.main === module) {
    const csvFilePath = process.argv[2];
    if (!csvFilePath) {
        console.error('Error: No CSV file path provided');
        console.log('Usage: pnpm exec ts-node scripts/import-organizers.ts <path/to/csv/file>');
        process.exit(1);
    }
    importOrganizers(csvFilePath)
        .then(() => process.exit(0))
        .catch(error => {
            console.error('Import failed:', error);
            process.exit(1);
        });
}

export { importOrganizers };
