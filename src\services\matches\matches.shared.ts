// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'

import type { ClientApplication } from '../../client'
import type { Match, MatchData, MatchPatch, MatchQuery, MatchesService } from './matches.class'

export type { Match, MatchData, MatchPatch, MatchQuery }

export type MatchClientService = Pick<MatchesService<Params<MatchQuery>>, (typeof matchesMethods)[number]>

export const matchesPath = 'matches'

export const matchesMethods: Array<keyof MatchesService> = ['find', 'get', 'create', 'patch', 'remove']

export const matchesClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(matchesPath, connection.service(matchesPath), {
    methods: matchesMethods
  })
}

// Add this service to the client service type index
declare module '../../client' {
  interface ServiceTypes {
    [matchesPath]: MatchClientService
  }
}
