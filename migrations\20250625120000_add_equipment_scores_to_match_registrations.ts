import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
    // Add equipmentId to match_registrations
    await knex.schema.alterTable('match_registrations', (table) => {
        table.integer('equipmentId').nullable().references('id').inTable('equipment')

        // Add index for equipment lookup
        table.index(['equipmentId'], 'idx_match_registrations_equipment_id')
    })

    // Add scores to match_results
    await knex.schema.alterTable('match_results', (table) => {
        table.jsonb('scores').nullable().comment('JSON scores data from points_json field')
    })
}

export async function down(knex: Knex): Promise<void> {
    // Remove scores from match_results
    await knex.schema.alterTable('match_results', (table) => {
        table.dropColumn('scores')
    })

    // Remove equipmentId from match_registrations
    await knex.schema.alterTable('match_registrations', (table) => {
        table.dropIndex(['equipmentId'], 'idx_match_registrations_equipment_id')
        table.dropColumn('equipmentId')
    })
}
