import type { Params } from '@feathersjs/feathers'

import type { ClientApplication } from '../../client'
import type {
  Tournament,
  TournamentData,
  TournamentPatch,
  TournamentQuery,
  TournamentsService
} from './tournaments.class'

export type { Tournament, TournamentData, TournamentPatch, TournamentQuery } // Updated exports

export type TournamentsClientService = Pick<
  TournamentsService<Params<TournamentQuery>>,
  (typeof tournamentsMethods)[number]
> // Updated type

export const tournamentsPath = 'tournaments'

export const tournamentsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const tournamentsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(tournamentsPath, connection.service(tournamentsPath), {
    methods: tournamentsMethods
  })
}

// Add this service to the client service type index
declare module '../../client' {
  interface ServiceTypes {
    [tournamentsPath]: TournamentsClientService
  }
}
