// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { KnexService } from '@feathersjs/knex'
import type { KnexAdapterOptions, KnexAdapterParams } from '@feathersjs/knex'

import type { Application } from '../../declarations'
import type {
  MatchResult,
  MatchResultData,
  MatchResultPatch,
  MatchResultQuery
} from './match-results.schema'

export type { MatchResult, MatchResultData, MatchResultPatch, MatchResultQuery }

export interface MatchResultParams extends KnexAdapterParams<MatchResultQuery> {}

// By default calls the standard Knex adapter service methods but can be customized with your own functionality.
export class MatchResultsService<ServiceParams extends Params = MatchResultParams> extends KnexService<
  MatchResult,
  MatchResultData,
  MatchResultParams,
  MatchResultPatch
> {}

export const getOptions = (app: Application): KnexAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('postgresqlClient'),
    name: 'match_results'
  }
}
