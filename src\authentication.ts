import axios from 'axios'
import { createSwaggerServiceOptions } from 'feathers-swagger' // Added for Swagger

import { AuthenticationService, JWTStrategy } from '@feathersjs/authentication'
import { LocalStrategy } from '@feathersjs/authentication-local'
import type { OAuthProfile } from '@feathersjs/authentication-oauth'
import { OAuthStrategy, oauth } from '@feathersjs/authentication-oauth'
import type { Params } from '@feathersjs/feathers'
import { Static, Type } from '@feathersjs/typebox' // Added for inline schema

import type { Application } from './declarations'

// Inline Schema for Authentication Data (Request)
const authenticationDataSchema = Type.Object(
  {
    strategy: Type.String(),
    email: Type.Optional(Type.String({ format: 'email' })),
    password: Type.Optional(Type.String()),
    accessToken: Type.Optional(Type.String()) // For token-based strategies
  },
  { $id: 'AuthenticationData', additionalProperties: true }
)
export type AuthenticationData = Static<typeof authenticationDataSchema>

// Inline Schema for Authentication Response
const authenticationSchema = Type.Object(
  {
    accessToken: Type.String(),
    authentication: Type.Object({
      strategy: Type.String(),
      accessToken: Type.String(),
      payload: Type.Object({}, { additionalProperties: true })
    }),
    // Using a generic object for user to avoid complex imports here
    user: Type.Object({}, { additionalProperties: true, $id: 'UserAuthentication' })
  },
  { $id: 'Authentication', additionalProperties: false }
)
export type Authentication = Static<typeof authenticationSchema>

// Inline Schema for Authentication Query (Optional, but good practice)
const authenticationQuerySchema = Type.Object(
  {
    strategy: Type.Optional(Type.String())
  },
  { $id: 'AuthenticationQuery', additionalProperties: false }
)
export type AuthenticationQuery = Static<typeof authenticationQuerySchema>

declare module './declarations' {
  interface ServiceTypes {
    authentication: AuthenticationService
  }
}

class GitHubStrategy extends OAuthStrategy {
  name = 'github';

  async getProfile(authResult: any, params: Params): Promise<OAuthProfile> {
    const profile = await super.getProfile(authResult, params);
    if (!profile.email) {
      const accessToken = authResult.access_token || authResult.accessToken;
      const { data: emails } = await axios.get('https://api.github.com/user/emails', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: 'application/vnd.github.v3+json'
        }
      });
  

      profile.emails = emails;
      const primary = emails.find((e: any) => e.primary) || emails[0];
      if (primary) {
        profile.email = primary.email;
      }
    }
    return profile;
  }

  async getEntityData(profile: OAuthProfile, existing: any, params: Params) {
    const baseData = await super.getEntityData(profile, existing, params)

    const email = profile.email ?? profile.emails?.[0]?.value;

    return {
      ...baseData,
      // The GitHub profile image
      avatar: profile.avatar_url,
      githubId: profile.login,
      // The user email address (if available)
      email
    }
  }
}

export const authentication = (app: Application) => {
  const authentication = new AuthenticationService(app)

  authentication.register('jwt', new JWTStrategy())
  authentication.register('local', new LocalStrategy())
  authentication.register('google', new OAuthStrategy())
  authentication.register('facebook', new OAuthStrategy())
  authentication.register('github', new GitHubStrategy())

  app.use('authentication', authentication, {
    docs: createSwaggerServiceOptions({
      schemas: {
        authenticationDataSchema, // Schema for POST request body
        authenticationSchema,     // Schema for successful authentication response (201 Created)
        authenticationQuerySchema // Schema for query parameters (if any)
      },
      docs: {
        description: `Service for user authentication.
Supports standard email/password (local) strategy, JWT, and OAuth providers (Google, Facebook, GitHub).
To initiate OAuth, redirect the user to \`/oauth/<provider_name>\` (e.g., \`/oauth/github\`).
The application will handle the OAuth flow and token exchange.
Upon successful OAuth authentication, the user will be redirected back to the application, and a session/token will be established similar to the local strategy.`,
        securities: ['all'], // Indicates that all operations may require security
        tags: ['auth']       // Groups authentication endpoints under "auth" tag in Swagger UI
      }
    })
  })
  app.configure(oauth())
}
