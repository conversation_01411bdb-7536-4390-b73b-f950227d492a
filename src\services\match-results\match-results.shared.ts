// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'

import type { ClientApplication } from '../../client'
import type {
  MatchResult,
  MatchResultData,
  MatchResultPatch,
  MatchResultQuery,
  MatchResultsService
} from './match-results.class'

export type { MatchResult, MatchResultData, MatchResultPatch, MatchResultQuery }

export type MatchResultsClientService = Pick<
  MatchResultsService<Params<MatchResultQuery>>,
  (typeof matchResultsMethods)[number]
>

export const matchResultsPath = 'match-results'

export const matchResultsMethods: Array<keyof MatchResultsService> = [
  'find',
  'get',
  'create',
  'patch',
  'remove'
]

export const matchResultsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(matchResultsPath, connection.service(matchResultsPath), {
    methods: matchResultsMethods
  })
}

// Add this service to the client service type index
declare module '../../client' {
  interface ServiceTypes {
    [matchResultsPath]: MatchResultsClientService
  }
}
