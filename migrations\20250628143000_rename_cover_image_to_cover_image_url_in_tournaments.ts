import { Knex } from 'knex'

// 20250628143000_rename_cover_image_to_cover_image_url_in_tournaments.ts
// Migration to rename 'coverImage' column to 'coverImageUrl' in 'tournaments' table

export async function up(knex: Knex): Promise<void> {
    await knex.schema.alterTable('tournaments', (table) => {
        table.renameColumn('coverImage', 'coverImageUrl')
    })
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.alterTable('tournaments', (table) => {
        table.renameColumn('coverImageUrl', 'coverImage')
    })
}
