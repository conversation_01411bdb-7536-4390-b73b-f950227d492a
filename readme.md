# ap-api-feathers

> Archery Points API

## About

This project uses [Feathers](http://feathersjs.com). An open source framework for building APIs and real-time applications.

## Getting Started

1. Make sure you have [NodeJS](https://nodejs.org/) and [npm](https://www.npmjs.com/) installed.
2. Install your dependencies

    ```
    cd path/to/ap-api-feathers
    npm install
    ```

3. Start your app

    ```
    npm run compile # Compile TypeScript source
    npm run migrate # Run migrations to set up the database
    npm start
    ```

## Testing

### Running Tests

The project is configured to use a separate local database for testing to avoid affecting your development database.

```bash
# Run tests using the local test database
pnpm test

# Run tests with a clean test database (drops and recreates the database)
pnpm test:clean

# Only set up the test database without running tests
pnpm setup-test-db

# Clean the test database (drop and recreate)
pnpm clean-test-db
```

The test database is named `archery_test` and is automatically created when running tests. All migrations will be applied to this database during the test run.

## Scaffolding

This app comes with a powerful command line interface for Feathers. Here are a few things it can do:

```
$ npx feathers help                           # Show all commands
$ npx feathers generate service               # Generate a new Service
```

## Help

For more information on all the things you can do with Feathers visit [docs.feathersjs.com](http://docs.feathersjs.com).
