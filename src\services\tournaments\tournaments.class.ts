import type { Params } from '@feathersjs/feathers'
import { KnexService } from '@feathersjs/knex'
import type { KnexAdapterOptions, KnexAdapterParams } from '@feathersjs/knex'

import type { Application } from '../../declarations'
import type {
  Tournament,
  TournamentData,
  TournamentPatch,
  TournamentQuery
} from './tournaments.schema'

export type { Tournament, TournamentData, TournamentPatch, TournamentQuery } // Updated exports

export interface TournamentParams extends KnexAdapterParams<TournamentQuery> {} // Updated interface name and type

// By default calls the standard Knex adapter service methods but can be customized with your own functionality.
export class TournamentsService<ServiceParams extends Params = TournamentParams> extends KnexService<
  Tournament, // Updated type
  TournamentData, // Updated type
  TournamentParams, // Updated type
  TournamentPatch // Updated type
> {}

export const getOptions = (app: Application): KnexAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('postgresqlClient'),
    name: 'tournaments'
  }
}
