import fs from 'fs';
import path from 'path';

import { parse } from 'csv-parse';

import { app } from '../src/app';
import type { Match, MatchData } from '../src/services/matches/matches.schema';

// Define an interface for Feathers error type
interface FeathersError extends Error {
  code: number;
  className: string;
  data?: any[];
}

/**
 * Convert string to boolean
 */
const parseBool = (value: string | undefined): boolean | undefined => {
  if (value === undefined || value === '' || value === '\\N') return undefined;
  if (value === '0') return false;
  if (value === '1') return true;
  return undefined;
};

/**
 * Parse JSON field from string 
 * Returns the original JSON string if valid, undefined if invalid/empty
 */
const parseJsonField = (value: string | undefined): string | undefined => {
  if (!value || value === '\\N' || value === '-1') return undefined;

  try {
    // Just validate that it's valid JSON, but return the original string
    JSON.parse(value);
    return value;
  } catch (error) {
    console.error(`Error parsing JSON value: ${value}`, error);
    return undefined;
  }
};

/**
 * Parse date fields from DD.MM.YYYY to YYYY-MM-DD
 */
const parseDate = (value: string | undefined): string | undefined => {
  if (!value || value === '\\N') return undefined;

  // Handle date format DD.MM.YYYY
  if (value.includes('.')) {
    const [day, month, year] = value.split('.');
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }

  return value;
};

/**
 * Parse timestamp fields from DD.MM.YYYY HH:MM or YYYY-MM-DD HH:MM:SS to ISO 8601 format (YYYY-MM-DDTHH:MM:SS.sssZ)
 */
const parseTimestamp = (value: string | undefined): string | undefined => {
  if (!value || value === '\\N') return undefined;

  console.log('parseTimestamp value:', value);

  // Handle date-time format DD.MM.YYYY HH:MM
  if (value.includes(' ') && value.includes('.')) {
    const [datePart, timePart] = value.split(' ');
    const [day, month, year] = datePart.split('.');

    // Format time part - add seconds if missing
    let formattedTime = timePart;
    if (!formattedTime.includes(':')) formattedTime += ':00';
    if (formattedTime.split(':').length === 2) formattedTime += ':00';

    // Create ISO 8601 date-time string
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T${formattedTime}.000Z`;
  }

  // Handle date-time format YYYY-MM-DD HH:MM:SS
  if (value.includes(' ') && value.includes('-')) {
    const [datePart, timePart] = value.split(' ');

    // Format time part - add seconds if missing
    let formattedTime = timePart;
    if (!formattedTime.includes(':')) formattedTime += ':00';
    if (formattedTime.split(':').length === 2) formattedTime += ':00';

    // Create ISO 8601 date-time string
    return `${datePart}T${formattedTime}.000Z`;
  }

  return value;
};

/**
 * Parse string fields, handling special null values
 */
const parseString = (value: string | undefined): string | undefined => {
  if (!value || value === '\\N' || value === 'NULL' || value === 'null' || value === '') {
    return undefined;
  }
  return value.trim();
};

/**
 * Parse number fields, handling special values and ensuring type safety
 */
const parseNumber = (value: string | undefined): number | undefined => {
  if (!value || value === '\\N' || value === '-1' || value === '') return undefined;

  const num = Number(value);
  if (isNaN(num)) {
    console.warn(`Failed to parse number: "${value}"`);
    return undefined;
  }

  return num;
};

/**
 * Main import function for matches from CSV
 */
async function importMatches(csvFilePath: string) {
  const matches = app.service('matches');
  const federations = app.service('federations');

  // Build federation lookup map: legacyId -> id
  const federationList = await federations.find({ paginate: false });
  const federationLookup = new Map<number, number>();

  if (Array.isArray(federationList)) {
    for (const federation of federationList) {
      if (federation.legacyId) {
        federationLookup.set(federation.legacyId, federation.id);
      }
    }
  }

  console.log(`Loaded ${federationLookup.size} federations for lookup`);

  // Ensure the path is absolute
  const filePath = path.isAbsolute(csvFilePath)
    ? csvFilePath
    : path.join(process.cwd(), csvFilePath);

  console.log(`Importing matches from ${filePath}`);

  const parser = fs
    .createReadStream(filePath)
    .pipe(parse({
      delimiter: ';', // Use comma as delimiter
      columns: true, // Use headers from CSV
      skip_empty_lines: true,
      relax_quotes: true,
      skip_records_with_empty_values: true
    }));

  let recordCount = 0;
  let importCount = 0;
  let errorCount = 0;

  for await (const record of parser) {
    recordCount++;

    // Debug: Log the first record to see column names
    if (recordCount === 1) {
      console.log('CSV Headers/Keys:', Object.keys(record));
      console.log('First key details:', {
        'first_key': Object.keys(record)[0],
        'first_key_length': Object.keys(record)[0]?.length,
        'first_key_charCodes': Object.keys(record)[0]?.split('').map(c => c.charCodeAt(0))
      });
      console.log('First few values:', {
        'quoted_id': record['"id"'],
        'id': record.id,
        'first_key_value': record[Object.keys(record)[0]],
        name: record.name,
        active: record.active
      });
    }

    // Skip completely empty rows
    if (Object.values(record).every(v => !v || v === '')) {
      continue;
    }

    try {
      // Lookup organizer by legacyId if organizer_id is present
      let resolvedOrganizerId: number | undefined = undefined;
      if (record.organizer_id) {
        const organizersService = app.service('organizers');
        const found = await organizersService.find({
          query: { legacyId: parseNumber(record.organizer_id) },
          paginate: false
        });
        if (Array.isArray(found) && found.length > 0) {
          resolvedOrganizerId = found[0].id;
          console.log('found organizer id:', resolvedOrganizerId)
        }
      }

      // Lookup federation by legacyId if federation is present
      let resolvedFederationId: number | undefined = undefined;
      if (record.federation && record.federation !== '\\N' && record.federation !== '-1') {
        const federationLegacyId = parseNumber(record.federation);
        if (federationLegacyId) {
          resolvedFederationId = federationLookup.get(federationLegacyId);
          if (resolvedFederationId) {
            console.log('found federation id:', resolvedFederationId, 'for legacyId:', federationLegacyId);
          } else {
            console.warn('Federation not found for legacyId:', federationLegacyId);
          }
        }
      }

      // Lookup tournament by legacyId if tournament_id is present
      let resolvedTournamentId: number | undefined = undefined;
      if (record.tournament_id && record.tournament_id !== '\\N' && record.tournament_id !== '-1') {
        const tournamentLegacyId = parseNumber(record.tournament_id);
        if (tournamentLegacyId) {
          const tournamentsService = app.service('tournaments');
          const found = await tournamentsService.find({
            query: { legacyId: tournamentLegacyId },
            paginate: false
          });
          if (Array.isArray(found) && found.length > 0) {
            resolvedTournamentId = found[0].id;
            console.log('found tournament id:', resolvedTournamentId, 'for legacyId:', tournamentLegacyId);
          } else {
            console.warn('Tournament not found for legacyId:', tournamentLegacyId);
          }
        }
      }

      // First create the required data according to the schema
      // Handle the quoted "id" field name issue - BOM and quotes can cause issues
      const firstKey = Object.keys(record)[0];
      let rawId = record[firstKey]; // Always use the first key which should be the ID

      const parsedLegacyId = parseNumber(rawId);
      console.log(`Record #${recordCount} - Using key "${firstKey}" -> Raw ID: "${rawId}" -> Parsed legacyId: ${parsedLegacyId}`);

      if (!parsedLegacyId) {
        console.warn(`No valid legacyId found for record ${recordCount}, skipping`);
        errorCount++;
        continue;
      }

      const matchData: MatchData = {
        legacyId: parsedLegacyId,
        name: parseString(record.name) || 'Unnamed Match',
        isActive: parseBool(record.active) ?? true,
        equipmentCategories: parseJsonField(record.equipment_categories),
        ageCategories: parseJsonField(record.age_categories),
        organizerId: resolvedOrganizerId,

        // other required fields for MatchData if any should be added here with default or parsed values
      };

      // Then add the additional data as a patch
      const additionalData: Partial<Match> = {
        isActive: parseBool(record.active) ?? true,
        country: parseString(record.country),
        city: parseString(record.city),
        postcode: parseString(record.postcode),
        address: parseString(record.address),
        phone: parseString(record.phone),
        email: parseString(record.email),
        startDate: parseDate(record.start),
        endDate: parseDate(record.end),
        registrationEnds: parseTimestamp(record.registration_deadline),
        description: parseString(record.description),
        coverImageUrl: parseString(record.photo),
        matchType: parseString(record.competition_branch),
        equipmentCategories: parseJsonField(record.equipment_categories),
        ageCategories: parseJsonField(record.age_categories),
        forWomen: parseBool(record.for_women),
        forMen: parseBool(record.for_men),
        federationId: resolvedFederationId,
        licenseRequired: parseBool(record.license_required),
        maxPlayersAmount: parseNumber(record.max_players_amount),
        payments: parseJsonField(record.payments),
        competitionLevel: parseString(record.competition_level),
        international: parseBool(record.international),
        withoutLimits: parseBool(record.without_limits),
        publishAt: parseTimestamp(record.public),
        completedAt: parseTimestamp(record.completed),
        latitude: record.latitude ? parseFloat(record.latitude.replace(',', '.')) : undefined,
        longitude: record.longitude ? parseFloat(record.longitude.replace(',', '.')) : undefined,
        agenda: parseJsonField(record.agenda),
        currency: parseString(record.currency),
        judges: parseJsonField(record.judges),
        registrationFinished: parseBool(record.registration_finished),
        attachments: parseJsonField(record.attachments),
        createdAt: parseTimestamp(record.create_date),
        tournamentId: resolvedTournamentId,
        tournamentConfirmedAt: parseTimestamp(record.tournament_confirmed),
        yearly: parseBool(record.yearly)
      };

      // Special handling for values that caused validation errors
      if (additionalData.publishAt === undefined) {
        delete additionalData.publishAt;
      }

      if (additionalData.completedAt === undefined) {
        delete additionalData.completedAt;
      }

      if (additionalData.tournamentId === undefined) {
        delete additionalData.tournamentId;
      }

      // Remove any undefined values to avoid schema validation issues
      Object.keys(additionalData).forEach(key => {
        if (additionalData[key as keyof Match] === undefined) {
          delete additionalData[key as keyof Match];
        }
      });

      // Debug for fields that caused validation errors
      if (record.public) {
        console.log(`Record #${recordCount} - publishAt field: ${record.public} -> ${additionalData.publishAt || 'removed'}`);
      }

      if (record.completed) {
        console.log(`Record #${recordCount} - completedAt field: ${record.completed} -> ${additionalData.completedAt || 'removed'}`);
      }

      if (record.tournament_id) {
        console.log(`Record #${recordCount} - tournamentId: ${record.tournament_id} -> ${additionalData.tournamentId || 'removed'}`);
      }

      // Create the match with required fields first, then update with additional data
      console.log(`Creating match with data:`, {
        legacyId: matchData.legacyId,
        name: matchData.name,
        isActive: matchData.isActive
      });

      const createdMatch = await matches.create(matchData);
      console.log(`Created match:`, {
        id: createdMatch.id,
        legacyId: createdMatch.legacyId,
        name: createdMatch.name
      });

      if (Object.keys(additionalData).length > 0) {
        const patchedMatch = await matches.patch(createdMatch.id, additionalData);
        console.log(`Patched match:`, {
          id: patchedMatch.id,
          legacyId: patchedMatch.legacyId,
          name: patchedMatch.name
        });
      }

      importCount++;
      console.log(`Imported match #${importCount}: ${matchData.name}`);
    } catch (error: unknown) {
      errorCount++;
      console.error(`Error importing record #${recordCount}:`, error);

      // Enhanced error logging with proper type checking
      if (error && typeof error === 'object' && 'data' in error) {
        const feathersError = error as FeathersError;
        if (Array.isArray(feathersError.data) && feathersError.data.length > 0) {
          console.error('Validation error details:', JSON.stringify(feathersError.data, null, 2));
          console.error('Problematic record data:', JSON.stringify({
            name: record.name,
            publishAt: record.public,
            completed: record.completed,
            tournamentId: record.tournament_id,
            active: record.active,
            start: record.start,
            end: record.end,
            registrationDeadline: record.registration_deadline,
            createDate: record.create_date
          }, null, 2));
        }
      }
    }
  }

  console.log(`Import complete: ${importCount} matches imported successfully, ${errorCount} errors`);
}

// If this script is run directly (not imported)
if (require.main === module) {
  const csvFilePath = process.argv[2];
  if (!csvFilePath) {
    console.error('Error: No CSV file path provided');
    console.log('Usage: pnpm exec ts-node scripts/import-matches.ts <path/to/csv/file>');
    process.exit(1);
  }

  // Run the import
  importMatches(csvFilePath)
    .then(() => process.exit(0))
    .catch(error => {
      console.error('Import failed:', error);
      process.exit(1);
    });
}

export { importMatches };
