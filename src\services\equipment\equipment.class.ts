// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { KnexService } from '@feathersjs/knex'
import type { KnexAdapterParams, KnexAdapterOptions } from '@feathersjs/knex'

import type { Application } from '../../declarations'
import type { Equipment, EquipmentData, EquipmentPatch, EquipmentQuery } from './equipment.schema'

export type { Equipment, EquipmentData, EquipmentPatch, EquipmentQuery }

export interface EquipmentParams extends KnexAdapterParams<EquipmentQuery> {}

// By default calls the standard Knex adapter service methods but can be customized with your own functionality.
export class EquipmentService<ServiceParams extends Params = EquipmentParams> extends KnexService<
  Equipment,
  EquipmentData,
  EquipmentParams,
  EquipmentPatch
> {}

export const getOptions = (app: Application): KnexAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('postgresqlClient'),
    name: 'equipment'
  }
}
