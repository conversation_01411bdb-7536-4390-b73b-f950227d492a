import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('equipment', table => {
    // Drop the original 'text' column
    table.dropColumn('text');
    
    // Add the new columns
    table.string('name').notNullable();
    table.string('category');
    table.string('equipmentClass');
    table.string('type');
    table.jsonb('configuration').comment('JSON configuration for the equipment');
    table.jsonb('federationStyles').comment('JSON array of federation styles');
    table.boolean('isActive').defaultTo(true);
    table.integer('playerId').references('id').inTable('players');
    table.boolean('isDefault').defaultTo(false);

    // Add timestamps for created/updated/deleted
    table.timestamp('createdAt').defaultTo(knex.fn.now());
    table.timestamp('updatedAt').defaultTo(knex.fn.now());
    table.timestamp('deletedAt').nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('equipment', table => {
    // Remove all new columns
    table.dropColumn('name');
    table.dropColumn('category');
    table.dropColumn('equipmentClass');
    table.dropColumn('type');
    table.dropColumn('configuration');
    table.dropColumn('federationStyles');
    table.dropColumn('isActive');
    table.dropColumn('playerId');
    table.dropColumn('isDefault');
    table.dropColumn('createdAt');
    table.dropColumn('updatedAt');
    table.dropColumn('deletedAt');
    
    // Re-add the original 'text' column
    table.string('text');
  });
}

