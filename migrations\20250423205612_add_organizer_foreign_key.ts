import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
  // Check if the column exists
  const hasColumn = await knex.schema.hasColumn('matches', 'organizerId')

  // First, drop the column if it exists
  if (hasColumn) {
    await knex.schema.alterTable('matches', table => {
      table.dropColumn('organizerId')
    })
  }

  // Add the organizerId column with a foreign key constraint
  await knex.schema.alterTable('matches', table => {
    table.integer('organizerId').nullable().references('id').inTable('organizers')

    // Add an index for faster lookups
    table.index(['organizerId'], 'idx_matches_organizer_id')
  })
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('matches', table => {
    // Remove the foreign key constraint and index
    table.dropIndex(['organizerId'], 'idx_matches_organizer_id')
    table.dropForeign(['organizerId'])

    // Keep the column but without the foreign key constraint
    table.integer('organizerId').nullable().alter()
  })
}
