import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('matches', table => {
    table.renameColumn('public', 'publishAt');
  });

  // Ensure the publishAt field is of timestamp type
  await knex.schema.alterTable('matches', table => {
    table.timestamp('publishAt').alter();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('matches', table => {
    // When rolling back, first ensure the column is of date type to match the original
    table.date('publishAt').alter();
    table.renameColumn('publishAt', 'public');
  });
}

