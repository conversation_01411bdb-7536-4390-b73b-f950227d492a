import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable('tournaments', table => {
    table.increments('id')
    table.integer('organizerId').references('id').inTable('organizers').notNullable()
    table.string('name').notNullable()
    table.text('description')
    table.string('coverImage')
    table.string('icon')
    table.boolean('isActive').defaultTo(true)
    table.timestamp('createdAt').defaultTo(knex.fn.now())
    table.timestamp('updatedAt').defaultTo(knex.fn.now())
    table.timestamp('deletedAt').nullable()
    table.integer('createdBy').nullable().references('id').inTable('users')
    table.integer('updatedBy').nullable().references('id').inTable('users')
    table.integer('deletedBy').nullable().references('id').inTable('users')
    
    // Add indexes for faster lookups
    table.index(['organizerId'], 'idx_tournaments_organizer_id')
    table.index(['isActive'], 'idx_tournaments_is_active')
  })
  
   await knex.schema.alterTable('matches', table => {
      table.boolean('tournamentConfirmed').defaultTo(false)
    }
  )
}

export async function down(knex: Knex): Promise<void> {
  // First remove the foreign key from matches
  await knex.schema.alterTable('matches', table => {
    table.dropColumn('tournamentConfirmed')

  })
  
  // Then drop the tournaments table
  await knex.schema.dropTable('tournaments')
}
