import assert from 'assert'

// test/services/email.test.ts
import { Application } from '@feathersjs/feathers';

import {app} from '../../../src/app';
import { EmailService } from '../../../src/services/email/email.class';
import { MockEmailProvider } from '../../../src/services/email/providers/mock-provider';
import { TestEmailProvider } from '../../../src/services/email/providers/test-provider';

describe('Email Service', () => {
  let emailService: EmailService;
  let testProvider: TestEmailProvider;

  beforeEach(() => {
    emailService = (app as any).service('_mailer') as EmailService;
    const provider = emailService.getProvider();
    
    // Check if we're getting the TestEmailProvider
    if (!(provider instanceof TestEmailProvider)) {
      throw new Error(`Expected TestEmailProvider but got ${provider.constructor.name}. Make sure NODE_ENV is set to 'test'.`);
    }
    
    testProvider = provider as TestEmailProvider;
    testProvider.clearSentEmails();
  });

  it('should send email successfully', async () => {
    const emailData = {
      to: '<EMAIL>',
      subject: 'Test Email',
      text: 'This is a test email'
    };

    await emailService.create(emailData);

    const sentEmails = testProvider.getSentEmails();
    assert.strictEqual(sentEmails.length, 1);
    assert.strictEqual(sentEmails[0].to, '<EMAIL>');
    assert.strictEqual(sentEmails[0].subject, 'Test Email');
  });

  it('should not be accessible externally', async () => {
    // Test that the service has external access disabled
    try {
      // Create a mock context that simulates external access
      const mockContext = { type: 'external' };
      const service = (app as any).service('/_mailer');
      
      // This should throw an error due to disallow('external') hook
      await service.hooks.before.all[0](mockContext);
      
      // If we reach here, the test should fail
      assert.fail('Expected external access to be disallowed');
    } catch (error: any) {
      // We expect an error to be thrown
      assert.ok(error);
    }
  });
});

describe('Email Provider Selection', () => {
  let originalNodeEnv: string | undefined;

  beforeEach(() => {
    originalNodeEnv = process.env.NODE_ENV;
  });

  afterEach(() => {
    if (originalNodeEnv !== undefined) {
      process.env.NODE_ENV = originalNodeEnv;
    } else {
      delete process.env.NODE_ENV;
    }
  });

  it('should use TestEmailProvider in test environment', () => {
    process.env.NODE_ENV = 'test';
    const emailService = new EmailService(app);
    const provider = emailService.getProvider();
    
    assert.ok(provider instanceof TestEmailProvider);
  });

  it('should use MockEmailProvider in development environment by default', () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';
    
    // Mock app without explicit email provider configuration
    const mockApp = {
      get: (key: string) => {
        if (key === 'email') {
          return { defaultFrom: '<EMAIL>' }; // No provider field
        }
        return (app as any).get(key);
      }
    } as Application;
    
    const emailService = new EmailService(mockApp);
    const provider = emailService.getProvider();
    
    assert.ok(provider instanceof MockEmailProvider);
    
    // Restore original environment
    process.env.NODE_ENV = originalEnv;
  });

  it('should use MockEmailProvider in staging environment by default', () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'staging';
    
    // Mock app without explicit email provider configuration
    const mockApp = {
      get: (key: string) => {
        if (key === 'email') {
          return { defaultFrom: '<EMAIL>' }; // No provider field
        }
        return (app as any).get(key);
      }
    } as Application;
    
    const emailService = new EmailService(mockApp);
    const provider = emailService.getProvider();
    
    assert.ok(provider instanceof MockEmailProvider);
    
    // Restore original environment
    process.env.NODE_ENV = originalEnv;
  });

  it('should respect explicit provider configuration', () => {
    process.env.NODE_ENV = 'development';
    
    // Mock app configuration with explicit provider
    const mockApp = {
      get: (key: string) => {
        if (key === 'email') {
          return { provider: 'mock' };
        }
        return (app as any).get(key);
      }
    } as Application;
    
    const emailService = new EmailService(mockApp);
    const provider = emailService.getProvider();
    
    assert.ok(provider instanceof MockEmailProvider);
  });
});
