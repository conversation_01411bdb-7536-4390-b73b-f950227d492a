// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import { authenticate } from '@feathersjs/authentication'

import { hooks as schemaHooks } from '@feathersjs/schema'

import {
  clubDataValidator,
  clubPatchValidator,
  clubQueryValidator,
  clubResolver,
  clubExternalResolver,
  clubDataResolver,
  clubPatchResolver,
  clubQueryResolver
} from './clubs.schema'

import type { Application } from '../../declarations'
import { ClubService, getOptions } from './clubs.class'
import { clubPath, clubMethods } from './clubs.shared'

export * from './clubs.class'
export * from './clubs.schema'

// A configure function that registers the service and its hooks via `app.configure`
export const club = (app: Application) => {
  // Register our service on the Feathers application
  app.use(clubPath, new ClubService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: clubMethods,
    // You can add additional custom events to be sent to clients here
    events: []
  })
  // Initialize hooks
  app.service(clubPath).hooks({
    around: {
      all: [
        authenticate('jwt'),
        schemaHooks.resolveExternal(clubExternalResolver),
        schemaHooks.resolveResult(clubResolver)
      ]
    },
    before: {
      all: [schemaHooks.validateQuery(clubQueryValidator), schemaHooks.resolveQuery(clubQueryResolver)],
      find: [],
      get: [],
      create: [schemaHooks.validateData(clubDataValidator), schemaHooks.resolveData(clubDataResolver)],
      patch: [schemaHooks.validateData(clubPatchValidator), schemaHooks.resolveData(clubPatchResolver)],
      remove: []
    },
    after: {
      all: []
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    [clubPath]: ClubService
  }
}
