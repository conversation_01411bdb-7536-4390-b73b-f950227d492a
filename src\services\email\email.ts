import { disallow } from 'feathers-hooks-common';

// src/services/email/email.service.ts
import { Application } from '@feathersjs/feathers';

import { EmailService } from './email.class';

const emailPath = '_mailer';

export const email = function (app: Application): void {
  // Register the internal email service
  app.use(emailPath, new EmailService(app));

  // Get the service
  const service = app.service('/_mailer');

  // Set up hooks to prevent external access
  service.hooks({
    before: {
      all: [disallow('external')],
      find: [disallow()],
      get: [disallow()],
      update: [disallow()],
      patch: [disallow()],
      remove: [disallow()]
    }
  });
}
// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    [emailPath]: EmailService
  }
}
