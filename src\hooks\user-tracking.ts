import type { HookContext } from '../declarations'

/**
 * Hook to automatically populate user tracking fields (createdBy, updatedBy, deletedBy)
 * This hook should be used in the before.create, before.patch, and before.remove hooks
 */
export const populateUserTracking = () => {
  return async (context: HookContext) => {
    // Skip if there's no authenticated user
    if (!context.params.user) {
      return context
    }

    const userId = context.params.user.id

    // For create operations, set createdBy
    if (context.method === 'create') {
      context.data.createdBy = userId

      // Also set updatedBy for consistency
      context.data.updatedBy = userId
    }

    // For patch/update operations, set updatedBy
    if (context.method === 'patch' || context.method === 'update') {
      context.data.updatedBy = userId

      // Also set updatedAt to ensure it's updated
      context.data.updatedAt = new Date().toISOString()
    }

    // For remove operations, set deletedBy and deletedAt
    // Note: This assumes soft delete pattern is used
    if (context.method === 'remove') {
      // In Feathers, remove doesn't normally modify data
      // So we need to patch the record first to set deletedBy and deletedAt
      const id = context.id

      if (id !== undefined) {
        // Get the service
        const service = context.service

        // Patch the record to set deletedBy and deletedAt
        await service.patch(
          id,
          {
            deletedBy: userId,
            deletedAt: new Date().toISOString()
          },
          // Use a new params object to avoid infinite loops
          { ...context.params, skipDeletedByHook: true }
        )
      }
    }

    return context
  }
}

/**
 * Hook to skip the populateUserTracking hook when skipDeletedByHook is set
 * This is used to prevent infinite loops when patching a record during removal
 */
export const skipIfDeletedByHook = () => {
  return async (context: HookContext) => {
    if (context.params.skipDeletedByHook) {
      return context
    }

    return context
  }
}
