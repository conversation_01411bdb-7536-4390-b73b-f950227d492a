import { Transporter, createTransport } from 'nodemailer';

import { EmailOptions, EmailProvider } from './email-provider.interface';

export class SMTPEmailProvider implements EmailProvider {
  private transporter: Transporter;

  constructor(config: any) {
    this.transporter = createTransport({
      host: config.host,
      port: config.port,
      secure: config.secure,
      auth: {
        user: config.user,
        pass: config.pass
      }
    });
  }

  async send(options: EmailOptions): Promise<any> {
    const mailOptions = {
      from: options.from,
      to: options.to,
      subject: options.subject,
      text: options.text,
      html: options.html,
      attachments: options.attachments
    };

    return await this.transporter.sendMail(mailOptions);
  }
}
